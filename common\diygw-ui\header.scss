.diygw-custom {
	display: block;
	position: relative;
}

.diygw-custom .diygw-bar .content {
	width: calc(100% - 440rpx);
}


.diygw-custom .diygw-bar .content image {
	height: 60rpx;
	width: 240rpx;
}

.diygw-custom .diygw-bar {
	min-height: 0px;
	padding-right: 220rpx;
	box-shadow: 0rpx 0rpx 0rpx;
	z-index: 9999;
}

.diygw-custom .diygw-bar .border-custom {
	position: relative;
	background: rgba(0, 0, 0, 0.15);
	border-radius: 1000rpx;
	height: 60rpx;
}

.diygw-custom .diygw-bar .border-custom::after {
	content: " ";
	width: 200%;
	height: 200%;
	position: absolute;
	top: 0;
	left: 0;
	border-radius: inherit;
	transform: scale(0.5);
	transform-origin: 0 0;
	pointer-events: none;
	box-sizing: border-box;
	border: 1rpx solid var(--white);
	opacity: 0.5;
}

.diygw-custom .diygw-bar .border-custom::before {
	content: " ";
	width: 1rpx;
	height: 110%;
	position: absolute;
	top: 22.5%;
	left: 0;
	right: 0;
	margin: auto;
	transform: scale(0.5);
	transform-origin: 0 0;
	pointer-events: none;
	box-sizing: border-box;
	opacity: 0.6;
	background-color: var(--white);
}

.diygw-custom .diygw-bar .border-custom text {
	display: block;
	flex: 1;
	margin: auto !important;
	text-align: center;
	font-size: 34rpx;
}


