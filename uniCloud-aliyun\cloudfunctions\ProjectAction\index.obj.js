// 云对象教程: https://uniapp.dcloud.net.cn/uniCloud/cloud-obj
// jsdoc语法提示教程：https://ask.dcloud.net.cn/docs/#//ask.dcloud.net.cn/article/129
const db = uniCloud.databaseForJQL()
const dbCmd = db.command
const { getStatus,getInviteStatus } = require('project-history')
const { convertPosition, convertStatus } = require('b_project')
const { getType } = require('identified')
const { second } = require('timestamp')

module.exports = {
	_before: function () { // 通用预处理器
		db.setUser({
			role: ['admin'], 
		})
	},
	// 申请加入项目（用户申请加入项目）
	async requestJoin(data){
		const checkProject = await db.collection('xm-stp-project_detail')
		.where({_id:data.project_id}).field('current_person_request').get()
		
		if(!checkProject.affectedDocs) return {
			status:0,
			msg:"项目不存在"
		}
		
		const check = await db.collection('xm-stp-project_app_request')
		.where({
			user_id:data.user_id,
			project_id:data.project_id,
		})
		.get()
		
		if(check.affectedDocs == 1) return {
			status:0,
			msg:"你已经申请过该项目"
		}
		const check2 = await db.collection('xm-stp-project_app_invite')
		.where({
			user_id:data.user_id,
			project_id:data.project_id,
		})
		.get()
		if(check2.affectedDocs == 1) return {
			status:0,
			msg:"你已被邀请于这个项目"
		}
		
		
		const transaction = await db.startTransaction()
		await db.collection('xm-stp-project_app_request').add({
			user_id:data.user_id,
			project_id:data.project_id,
			comment:data.introduce,
			status:0
		})
		
		await db.collection('xm-stp-project_app_history').add({
			user_id:data.user_id,
			project_id:data.project_id,
			action:parseInt(getStatus('申请加入'))
		})
		
		
		await db.collection('xm-stp-project_detail').where({_id:data.project_id})
		.update({
			'current_person_request':checkProject.data[0].current_person_request + 1
		})
		
		// todo 可以考虑发消息给用户
		
		await transaction.commit()
		
		return {
			status:1,
			msg:"申请成功"
		}
	},
	// 邀请加入项目（项目拥有者邀请别人）
	async inviteJoin(data){
		if(data.self_user == data.user_id) return {
			status: 0,
			msg: "不允许自己邀请自己"
		}
		
		const res = await db.collection('xm-stp-project_detail').where({
			_id:data.proj_id,
			user_id:data.self_user
		}).field('_id').get()
		if(!res.affectedDocs) return {
			status: 0,
			msg: "项目不存在"
		}
		
		let check = await db.collection('xm-stp-project_app_invite').where({
			user_id: data.user_id,
			project_id:data.proj_id
		}).field('_id').get()
		
		if(check.affectedDocs) return {
			status: 0,
			msg: "该用户已经有被邀请过"
		}
		
		check = await db.collection('xm-stp-project_app_request').where({
			user_id: data.user_id,
			project_id:data.proj_id
		}).field('_id').get()
		
		if(check.affectedDocs) return {
			status: 0,
			msg: "该用户已有申请加入你的项目，请记得去过目"
		}
		
		const transaction = await db.startTransaction()
		
		await db.collection('xm-stp-project_app_invite').add({
			user_id: data.user_id,
			project_id:data.proj_id,
			status:0,
			comment:data.self_introduce
		})
		
		await db.collection('xm-stp-project_app_history').add({
			user_id:data.user_id,
			project_id:data.proj_id,
			action:parseInt(getStatus('发出邀请'))
		})
		
		await transaction.commit()
		
		return {
			status:1,
			msg:"邀请成功"
		}
	},
	async getInvitedList(data){
		const res = await db.collection('xm-stp-project_app_invite')
		.where({'user_id':data.user_id})
		.field('project_id,status,comment,create_time')
		.orderBy('create_time desc')
		.get()
		
		if(res.affectedDocs == 0) return {
			status:1,
			msg:"OK",
			data:[]
		}
	},
	async getJoinList(data){
		const res = await db.collection('xm-stp-project_app_request')
		.where({'user_id':data.user_id})
		.field("project_id,status,create_time, `request` as type")
		.get()
		
		const res2 = await db.collection('xm-stp-project_detail_user_rel').where({'user_id':data.user_id})
		.field("project_id,project_position as status,`position` as type")
		.get()
		
		res.data = [...res.data,...res2.data]
		
		console.log(res.data)
		
		if(!res.data.length) return {
			status:1,
			msg:"OK",
			data:[]
		}
		
		const projIds = []
		for(const i in res.data)
			projIds.push(res.data[i].project_id)
		
		const projectDetail = await db.collection('xm-stp-project_detail')
			.where({
				'_id':dbCmd.in(projIds)
			})
			.field('title,person_needed,create_time')
			.get()
		
			
		for(const i1 in res.data){
			for(const i2 in projectDetail.data){
				if(res.data[i1].project_id == projectDetail.data[i2]._id){
					res.data[i1].title = projectDetail.data[i2].title
					res.data[i1].person_needed = projectDetail.data[i2].person_needed
					
					res.data[i1].status = res.data[i1].type == 'request'?
					getStatus(res.data[i1].status):convertPosition(res.data[i1].status)
					
					if(res.data[i1].type == 'position')
					res.data[i1].create_time = projectDetail.data[i2].create_time
					
					break
				}
			}
		}
		
		
		const project = await db.collection('xm-stp-project')
			.where({
				'_id':dbCmd.in(projIds)
			})
			.field('type_id,competition_id,ending_time')
			.get()
		
		const prjTypeList = []
		const compIds = []
		for(const i in project.data){
			prjTypeList.push(project.data[i].type_id)
			
			if(project.data[i].competition_id) 
				compIds.push(project.data[i].competition_id)
		}
		
		const projectTypeDb = await db.collection('xm-stp-project_cat').where({
			_id: dbCmd.in([...new Set(prjTypeList)])
		}).get()
		
		for(const i1 in project.data){
			for(const i2 in projectTypeDb.data){
				if(project.data[i1].type_id == projectTypeDb.data[i2]._id){
					project.data[i1].type = projectTypeDb.data[i2].name
					delete project.data[i1].type_id
					break
				}
			}
		}
		
		if(compIds.length){
			const compList = await db.collection('xm-stp-project_comp_detail')
			.where({
				_id: dbCmd.in([...new Set(compIds)])
			})
			.field('title').get()
			
			for(const i1 in project.data){
				for(const i2 in compList.data){
					if(project.data[i1].competition_id == compList.data[i2]._id){
						project.data[i1].comp = compList.data[i2].title
						delete project.data[i1].competition_id
						break
					}
				}
			}
		}
		
		for(const i1 in res.data){
			for(const i2 in project.data){
				if(res.data[i1].project_id == project.data[i2]._id){
					res.data[i1].ending_time = project.data[i2].ending_time
					res.data[i1].type = project.data[i2].type
					
					if(project.data[i2].comp) res.data[i1].comp = project.data[i2].comp
					break
				}
			}
		}
		
		const $ = db.command.aggregate
		const counter = await db.collection('xm-stp-project_app_request').aggregate()
		    .match({
		        project_id: dbCmd.in(projIds) // 条件过滤：project_id 在指定列表中
		    })
		    .group({
		        _id: '$project_id', // 按 project_id 分组
		        count: $.sum(1) // 统计每组的数量
		    })
		    .end();
		
		for(const i1 in counter.data){
			for(const i2 in res.data){
				if(res.data[i2].project_id == counter.data[i1]._id){
					res.data[i2].person_request = counter.data[i1].count
					break
				}
			}
		}
		
		return {
			status:1,
			msg:"OK",
			data:res.data
		}
	},
	async getListFromMsg(data){
		const selfCond = `user_id == '${data.user_id}' && status == 1 && ending_time > ${second()}`
		const selfProj = await db.collection('xm-stp-project')
		.where(selfCond)
		.field('_id')
		.orderBy('create_time','desc')
		.limit(3)
		.get()
		
		if(selfProj.affectedDocs){
			const projIds = []
			for(const i in selfProj.data) projIds.push(selfProj.data[i]._id)
			
			const selfProjDetail = await db.collection('xm-stp-project_detail')
			.where({
				_id:dbCmd.in(projIds)
			})
			.field('title,current_person_request')
			.get()
			
			for(const i1 in selfProj.data){
				for(const i2 in selfProjDetail.data){
					if(selfProj.data[i1]._id == selfProjDetail.data[i2]._id){
						selfProj.data[i1] = { ...selfProj.data[i1], ...selfProjDetail.data[i2]}
						break
					}
				}
			}
			
		}
		
		const invitedProj = await db.collection('xm-stp-project_app_invite')
		.where({'user_id':data.user_id,'status':parseInt(getInviteStatus('等待回复'))})
		.field('project_id,create_time,comment')
		.orderBy('create_time desc')
		.get()
		
		if(invitedProj.affectedDocs){
			const projIds = []
			for(const i in invitedProj.data) projIds.push(invitedProj.data[i].project_id)
			
			const invitedProjDetail = await db.collection('xm-stp-project_detail')
			.where({
				_id:dbCmd.in(projIds)
			})
			.field('title')
			.get()
			
			for(const i1 in invitedProj.data){
				for(const i2 in invitedProjDetail.data){
					if(invitedProj.data[i1].project_id == invitedProjDetail.data[i2]._id){
						invitedProj.data[i1] = { ...invitedProj.data[i1], ...invitedProjDetail.data[i2]}
						break
					}
				}
			}
			
		}
		
		return {
			status:1,
			msg:'OK',
			data:{
				invite_list:invitedProj.data,
				request_list:selfProj.data
			}
		}
	}
	,
	async approveJoin(data){
		const check = await db.collection('xm-stp-project_app_invite')
		.where({
			project_id:data.project_id,
			user_id:data.user_id
		})
		.get()
		
		if(!check.affectedDocs) return{
			status:0,
			msg:"项目邀请不存在"
		}
		else if(check.data[0].status == getInviteStatus('已接受')) 
		return {
			status:0,
			msg: "项目早已接受"
		}
		
		const user = await db.collection('xm-stp-user_detail')
		.doc(data.user_id)
		.field('type')
		.get()
		
		const userType = await getType(user.data[0].type)
		
		const transaction = await db.startTransaction()
		
		await db.collection('xm-stp-project_app_invite')
		.where({
			project_id:data.project_id,
			user_id:data.user_id
		})
		.update({
			status: parseInt(getInviteStatus('已接受'))
		})
		
		await db.collection('xm-stp-project_detail_user_rel')
		.add({
			project_id:data.project_id,
			user_id:data.user_id,
			project_position: userType.name == "老师"?0:2
		})
		
		await db.collection('xm-stp-project_app_history').add({
			user_id:data.user_id,
			project_id:data.project_id,
			action:parseInt(getStatus('接受邀请'))
		})
		
		await transaction.commit()
		
		return {
			status: 1,
			msg: "接受邀请成功"
		}
	},
	async declineJoin(data){
		const check = await db.collection('xm-stp-project_app_invite')
		.where({
			project_id:data.project_id,
			user_id:data.user_id
		})
		.get()
		
		if(!check.affectedDocs) return{
			status:0,
			msg:"项目邀请不存在"
		}
		else if(check.data[0].status == getInviteStatus('已拒绝')) 
		return {
			status:0,
			msg: "项目早已拒绝"
		}
		
		
		
		const transaction = await db.startTransaction()
		
		await db.collection('xm-stp-project_app_invite')
		.where({
			project_id:data.project_id,
			user_id:data.user_id
		})
		.update({
			status: parseInt(getInviteStatus('已拒绝'))
		})
		
		await db.collection('xm-stp-project_detail_user_rel')
		.where({
			project_id:data.project_id,
			user_id:data.user_id
		}).delete()
		
		await db.collection('xm-stp-project_app_history').add({
			user_id:data.user_id,
			project_id:data.project_id,
			action:parseInt(getStatus('拒绝邀请'))
		})
		
		await transaction.commit()
		
		return {
			status: 1,
			msg: "拒绝邀请成功"
		}
	},
	async updateProjectMembers(data){
		const check = await db.collection('xm-stp-project_detail')
		.where({_id:data.id,user_id:data.user_id}).field('_id').get()
		if(!check.affectedDocs) return {
			status:0,
			msg:'不存在该项目'
		}
		
		/**
		 * 这里分成3种情况
		 * 1. 从申请中被选入待定甚至是内定成员
		 * 2. 从待定或内定成员被踢回申请中（如果是邀请来的则最低只能在待定）
		 * 3. 待定和内定成员之间变换
		 * 
		 * 1 的情况是需要再project的user_rel里边注册，并且在request表进行更新
		 * 2 是需要从user_rel删除，在request表中更新
		 * 3 的则只需要在user_rel里边调整就行了
		 */
		const list = {unpickedToMember:[], memberToUnpicked:[],  memberAction:[]}
		for(const i in data.data){
			
			switch(data.data[i].from){
				case 'pending':
				case 'confirm':
					if(['pending','confirm'].includes(data.data[i].to)) list.memberAction.push(data.data[i])
					else if(data.data[i].to == 'unpicked') list.memberToUnpicked.push(data.data[i])
				break;
				case 'unpicked':
					list.unpickedToMember.push(data.data[i])
				break;
			}
			
		}
		
		// 做2
		if(list.memberToUnpicked.length){
			const userList = []
			for(const i in list.memberToUnpicked) userList.push(list.memberToUnpicked[i].user_id)
			
			const checkInvited = await db.collection('xm-stp-project_app_invite').where({
				'user_id':dbCmd.in(userList)
			}).get()
			
			if(checkInvited.affectedDocs) return {
				status:0,
				msg:"你邀请的成员是不能放入（还未选上），若不要改成员请放在待定成员中"
			}
			
			await db.collection('xm-stp-project_detail_user_rel').where({
				'user_id':dbCmd.in(userList),
				'project_id':data.id
			}).remove()
			
			await db.collection('xm-stp-project_app_request').where({
				user_id:dbCmd.in(userList),
				project_id:data.id
			})
			.update({
				status:0
			})
			
		}
		
		// 做3
		if(list.memberAction.length){
			const pendingToConfirm = []
			const confirmToPending = []
			
			for(const i in list.memberAction){
				if(list.memberAction[i].from == 'pending' && list.memberAction[i].to == 'confirm')
					pendingToConfirm.push(list.memberAction[i].user_id)
				else
					confirmToPending.push(list.memberAction[i].user_id)
			}
			
			if(pendingToConfirm.length)
				await db.collection('xm-stp-project_detail_user_rel').where({
					'user_id':dbCmd.in(pendingToConfirm),
					'project_id':data.id
				}).update({
					'project_position':parseInt(convertPosition('成员')) 
				})
			
			if(confirmToPending.length)
				await db.collection('xm-stp-project_detail_user_rel').where({
					'user_id':dbCmd.in(confirmToPending),
					'project_id':data.id
				}).update({
					'project_position':parseInt(convertPosition('待定成员'))
				})
		}
		
		// 做1
		if(list.unpickedToMember.length){
			const toPending = []
			const toConfirm = []
			for(const i in list.unpickedToMember){
				if(list.unpickedToMember[i].to == 'pending')
					toPending.push(list.unpickedToMember[i].user_id)
				else
					toConfirm.push(list.unpickedToMember[i].user_id)
			}
				
			await db.collection('xm-stp-project_app_request').where({
				user_id:dbCmd.in(toPending.concat(toConfirm)),
				project_id:data.id
			})
			.update({
				status:1
			})
			
			if(toPending.length){
				const list = []
				for(const i in toPending) 
					list.push({
					'user_id':toPending[i],
					'project_id':data.id,
					'project_position':parseInt(convertPosition('待定成员'))
				})
				
				await db.collection('xm-stp-project_detail_user_rel').add(list)
			}
				
			
			if(toConfirm.length){
				const list = []
				for(const i in toConfirm) 
					list.push({
					'user_id':toConfirm[i],
					'project_id':data.id,
					'project_position':parseInt(convertPosition('成员'))
				})
				
				await db.collection('xm-stp-project_detail_user_rel').add(list)
			}
		}
		
		return {
			status:1,
			msg:"更新成功"
		}
	}
}
