<template>
	<view class="container container328924">
		<view class="flex diygw-col-24 justify-center items-center flex-nowrap flex-clz">
			<view class="flex flex-wrap diygw-col-23 flex-direction-column flex1-clz">
				<image :src="globalData.news.avatar.url" class="response diygw-col-24 image-clz" mode="widthFix"></image>
				<rich-text :nodes="globalData.news.title" class="diygw-col-24 text-clz"></rich-text>
				<view class="flex flex-wrap diygw-col-24 flex2-clz">
					<rich-text :nodes="text1" class="diygw-col-0 text1-clz"></rich-text>
					<text class="diygw-col-0"> 央视网 发布时间：2024年10月10日 13:43 </text>
				</view>
				<rich-text :nodes="globalData.news.paragraph" class="diygw-col-24"></rich-text>
			</view>
		</view>
		<view class="clearfix"></view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				//用户全局信息
				userInfo: {},
				//页面传参
				globalOption: {},
				//自定义全局变量
				globalData: { news: { avatar: { url: '' } } },
				text: `<p><span style="font-size: 14px;"><strong>运营机构要切实履行数据安全的主体责任</strong></span></p>`,
				text1: `<p>微视频</p>`,
				text3: `<p>&nbsp; &nbsp; &nbsp; &nbsp; &ldquo;可以说，数据资源开发利用程度越深，数据安全和个人信息保护就越要强化。&rdquo;10月10日，国新办举行新闻发布会介绍公共数据资源开发利用有关情况。国家数据局数据资源司司长张望介绍，《意见》强调了要在维护国家数据安全、保护个人信息和商业秘密的前提下，推动有序开发利用。特别是重申了应当保密的公共数据不予开放，严格管控未依法依规公开的原始公共数据直接进入市场，严禁运营机构未经授权超范围使用数据。</p>
<p>&nbsp; &nbsp; &nbsp; 具体来说，《意见》从制度建设、能力建设和过程管理三个方面提出了要求：</p>
<p>&nbsp; &nbsp; &nbsp; 在强化制度建设方面，《意见》明确，各地区、各部门要加强对授权运营工作的统筹管理，建立健全数据分级分类、风险评估、监测预警、应急处置等工作体系，开展公共数据授权利用的安全风险评估和业务规范性审查。数据管理机构要加强指导和管理。运营机构要切实履行数据安全的主体责任，采取必要措施，保障数据安全。特别是对于其中涉及个人信息的公共数据，要严格落实《个人信息保护法》，进行脱敏和匿名化处理，避免侵犯个人的信息权益。</p>
<p style="margin: 0px; line-height: 37px; font-size: 20px; text-indent: 2em; color: #47413f; font-family: 'PingFangSC-Regular,Helvetica,Arial,Microsoft Yahei,sans-serif'; text-align: justify; background-color: #ffffff;">&nbsp;</p>`
			};
		},
		onShow() {
			this.setCurrentPage(this);
		},
		onLoad(option) {
			this.setCurrentPage(this);
			if (option) {
				this.setData({
					globalOption: this.getOption(option)
				});
			}

			this.init();
		},
		methods: {
			async init() {
				await this.getNewsDetailFunction();
			},
			// 获取新闻详情 自定义方法
			async getNewsDetailFunction(param) {
				let thiz = this;
				let option = param && (param.option || param.option == 0) ? param.option : thiz.globalOption || '';
				const res = await uniCloud.importObject('News').getDetail(option.id);
				if (res.status == 0) {
					uni.showToast({
						title: res.msg,
						icon: 'error',
						duration: 2000
					});
					setTimeout(function () {
						uni.redirectTo({
							url: '/pages/home'
						});
					}, 2000);
				}

				this.globalData.news = res.data;
			}
		}
	};
</script>

<style lang="scss" scoped>
	.flex-clz {
		flex-shrink: 0;
		width: 100% !important;
	}
	.flex1-clz {
		margin-left: 0rpx;
		flex-shrink: 0;
		width: calc(95.8333333333% - 0rpx - 0rpx) !important;
		margin-top: 12rpx;
		margin-bottom: 16rpx;
		margin-right: 0rpx;
	}
	.image-clz {
		border-bottom-left-radius: 12rpx;
		overflow: hidden;
		border-top-left-radius: 12rpx;
		border-top-right-radius: 12rpx;
		border-bottom-right-radius: 12rpx;
	}
	.text-clz {
		margin-left: 0rpx;
		width: calc(100% - 0rpx - 0rpx) !important;
		margin-top: 14rpx;
		margin-bottom: 16rpx;
		margin-right: 0rpx;
	}
	.flex2-clz {
		margin-left: 2rpx;
		width: calc(100% - 2rpx - 0rpx) !important;
		margin-top: 0rpx;
		margin-bottom: 30rpx;
		margin-right: 0rpx;
	}
	.text1-clz {
		margin-left: 0rpx;
		margin-top: 0rpx;
		margin-bottom: 0rpx;
		margin-right: 40rpx;
	}
	.container328924 {
	}
</style>
