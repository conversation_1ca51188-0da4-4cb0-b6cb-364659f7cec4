/* ==================
         魔方布局
 ==================== */
.diygw-cubes {
  width:100vw !important;
  position: relative;
  height:100vw;
  display: flex;
  align-items: center;
  justify-content: space-around;
  overflow: hidden;

  .diygw-cube-item{
    position: absolute;
    display: flex;
    align-items: center;
    justify-content: space-around;
    overflow: hidden;

    .diygw-cube-img{
      object-fit: cover;
      height: 100%;
      width: 100%;
    }
  }
}