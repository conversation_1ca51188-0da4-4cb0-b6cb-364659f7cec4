

/* grid布局 */

.diygw-grid {
	display: flex;
	flex-wrap: wrap;
	flex: 1;
    
    .diygw-grid-title{
        white-space: nowrap;
    }
    &.scroll-view{
		flex-wrap: nowrap;
		white-space: nowrap;
		overflow-x: auto;
	}
	
	[class*="bg-"] .diygw-avatar{
		color: #fff;
	}
	uni-button,button{
		color: inherit;
	}
}


.diygw-grid-inner{
	display: flex;
	align-items: center;
	justify-content: space-around;
	flex-direction: column;
	text-align: center;
	padding:20rpx;
	position: relative;
	background-position: center;
	background-repeat: no-repeat;
}

.diygw-grid-inner.border::after {
	position: absolute;
	box-sizing: border-box;
	content: ' ';
	pointer-events: none;
	top: -50%;
	right: -50%;
	bottom: -50%;
	left: -50%;
	border: 0 solid #ebedf0;
	-webkit-transform: scale(0.5);
	transform: scale(0.5);
	z-index: 1;
	border-width: 0 1rpx 1rpx 0;
}

.diygw-grid-icon{
	width:48px;
	height:48px;
}
.diygw-grid.col-1 {
	.diygw-grid-item{
		width: 100%;
	}

}

.diygw-grid.col-2{
	.diygw-grid-item{
		width: 50%;
	}

}

.diygw-grid.col-3{
	.diygw-grid-item{
		width: 33.33%;
	}

}

.diygw-grid.col-4 {
	.diygw-grid-item{
		width: 25%;
	}

}

.diygw-grid.col-5 {
	.diygw-grid-item{
		width: 20%;
	}

}

.diygw-grid .diygw-avatar{
	background-color: transparent;
	font-size: 80rpx;
	color:#333;
	.diygw-tag[class*="diy-icon-"]{
		font-size: 20rpx;
	}
}


.diygw-grid.diygw-actions{
	background: inherit;
	align-items: center;

	button.diygw-action{
	    border-radius: 0;
		border: 0px;
		background:none;
		position: relative;
		border: 0px;
		display: inline-flex;
		align-items: center;
		justify-content: center;
		box-sizing: border-box;
		line-height: 1;
		text-align: center;
		text-decoration: none;
		overflow: visible;
		transform: translate(0px, 0px);
		&.radius{
			border-radius: 2000px;
		}
	}
	button.diygw-action::after {
		display: none;
	}

	.diygw-action{
		flex:1;
		padding: 6rpx 0;

		&[class*="bg-"]{
			flex:2;
			[class*="diy-icon-"]{
				color: inherit;
			}
		}
		&.radius-right{
			margin-right: 10rpx;
		}
		&.radius-left{
			margin-left: 10rpx;
		}
		&.radius{
			margin-right: 10rpx;
			margin-left: 10rpx;
		}
		.diygw-grid-inner{
			padding:0;
			flex:1;
		}
		.diygw-avatar{
			width:60rpx;
			height:60rpx;
			font-size:60rpx;
		}
		.diygw-grid-inner.border::after{
			border-width: 0 1rpx 0 0;
		}

		.diygw-grid-title{
			&.not-avatar{
				font-size:16px;
				line-height: 80rpx;
			}
		}

		&.addon {
			position: relative;
			z-index: 2;
			padding-top: 60rpx;
			background-color: inherit;

			.diygw-grid-title{
				z-index: 2;
			}

			.diygw-grid-icon {
				position: absolute;
				max-width: 70rpx;
				z-index: 2;
				max-height: 70rpx;
				border-radius: 50%;
				line-height:70rpx;
				font-size: 50rpx;
				top: -64rpx;
				left: 0;
				right: 0;
				margin: auto;
				padding: 0;
			}

			

			&:before {
				position: absolute;
				width: 100rpx;
				height: 100rpx;
				top: -24rpx;
				left: 0;
				right: 0;
				margin: auto;
				content: "";
				box-shadow: 0 -4rpx 8rpx rgba(0, 0, 0, 0.08);
				border-radius: 50rpx;
				background-color: inherit;
				z-index: 0;
			}
		}
	}
	.diygw-action:last-child{
		.diygw-grid-inner.border::after,.diygw-grid-inner.border::solid-right{
			border-width: 0;
		}
	}
}
