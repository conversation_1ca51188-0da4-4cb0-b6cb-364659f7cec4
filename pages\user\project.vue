<template>
	<view class="container container328924">
		<view class="flex flex-wrap diygw-col-24 flex-direction-column justify-center items-center">
			<text class="diygw-col-0 text-clz"> {{ globalData.user.real_name }} ({{ globalData.user.onboarding_year }} {{ globalData.user.type }}) </text>
			<text class="diygw-col-0 text22-clz"> {{ globalData.user.college }} {{ globalData.user.specific ? '-' : '' }} {{ globalData.user.specific }} </text>
			<button @tap="navigateTo" data-type="page" data-url="/pages/user/invite_to_project" :data-user_id="globalOption.user_id" class="diygw-col-11 btn-clz diygw-btn-default">申请加入我的项目</button>
		</view>
		<view class="flex flex-wrap diygw-col-24 flex-direction-column">
			<view v-for="(item, index) in globalData.list" :key="index" class="flex flex-wrap diygw-col-24 flex5-clz">
				<view class="flex flex-wrap diygw-col-15 flex-direction-column flex3-clz" @tap="navigateTo" data-type="page" data-url="/pages/project/detail" :data-id="item._id">
					<text class="diygw-col-0 text1-clz">
						{{ item.title }}
					</text>
					<text class="diygw-col-0 text2-clz">
						{{ item.type }}
					</text>
					<text class="diygw-col-0 text3-clz"> 申请人数：{{ item.person_needed }} 人 </text>
					<text class="diygw-col-0 text4-clz"> 已加入： {{ item.current_person_request ?? 0 }} </text>
					<text class="diygw-col-24 text6-clz"> 创建时间： {{ $tools.formatDateTime(item.create_time, 'YYYY-mm-dd HH:MM') }} </text>
				</view>
			</view>
		</view>
		<view class="clearfix"></view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				//用户全局信息
				userInfo: {},
				//页面传参
				globalOption: {},
				//自定义全局变量
				globalData: { user: { real_name: '' } }
			};
		},
		onShow() {
			this.setCurrentPage(this);
		},
		onLoad(option) {
			this.setCurrentPage(this);
			if (option) {
				this.setData({
					globalOption: this.getOption(option)
				});
			}

			this.init();
		},
		methods: {
			async init() {
				await this.getDetailFunction();
			},
			// 获取用户详情 自定义方法
			async getDetailFunction(param) {
				let thiz = this;
				let option = param && (param.option || param.option == 0) ? param.option : thiz.globalOption || '';
				const res = await uniCloud.importObject('User').getUserDetail({ user_id: option.user_id });
				if (!res.status) {
					uni.showToast({
						title: '用户不存在',
						icon: 'error'
					});

					uni.redirectTo({
						url: '/pages/home'
					});
				}

				this.globalData = res.data;
			}
		}
	};
</script>

<style lang="scss" scoped>
	.text-clz {
		font-size: 26rpx !important;
	}
	.text22-clz {
		font-size: 26rpx !important;
	}
	.btn-clz {
		background-color: #07c160;
		padding-top: 20rpx;
		color: #fff;
		padding-left: 20rpx;
		padding-bottom: 20rpx;
		text-align: center;
		padding-right: 20rpx;
	}
	.flex5-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.flex3-clz {
		flex: 1;
	}
	.text1-clz {
		margin-left: 10rpx;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text2-clz {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text3-clz {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text4-clz {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text6-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.container328924 {
	}
</style>
