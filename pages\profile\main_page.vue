<template>
	<view class="container container328924">
		<view class="flex flex-wrap diygw-col-24 justify-center items-center">
			<view class="flex flex-wrap diygw-col-24 justify-center items-center flex9-clz">
				<view class="flex flex-wrap diygw-col-14 flex-direction-column justify-center items-center flex10-clz">
					<text class="diygw-col-0 text7-clz">
						{{ globalData.userDetail.real_name }}
					</text>
					<text class="diygw-col-0 text8-clz"> {{ globalData.userDetail.college_category.name }} {{ Object.keys(globalData.userDetail.specific_category).length ? '-' : '' }} {{ globalData.userDetail.specific_category.name }} </text>
					<text class="diygw-col-0 text9-clz"> {{ globalData.userDetail.onboarding_year }}级（{{ globalData.userDetail.type.name }}） </text>
				</view>
			</view>
			<view class="flex flex-wrap diygw-col-24 justify-center flex1-clz">
				<view class="flex flex-wrap diygw-col-12 flex-direction-column justify-center items-center flex2-clz" @tap="navigateTo" data-type="page" data-url="/pages/in_project/self">
					<text class="diygw-col-0 diygw-text-xl"> 我的项目 </text>
				</view>
				<view class="flex flex-wrap diygw-col-12 flex-direction-column justify-center items-center flex3-clz" @tap="navigateTo" data-type="page" data-url="/pages/in_project/other">
					<text class="diygw-col-0 diygw-text-xl"> 我加入项目 </text>
				</view>
			</view>
			<view class="flex flex-wrap diygw-col-24 items-center flex4-clz">
				<text class="flex icon diygw-col-0 icon-clz diy-icon-mobile"></text>
				<text class="diygw-col-0 text2-clz"> 跟换手机号 </text>
			</view>
			<view class="flex flex-wrap diygw-col-24 items-center flex7-clz">
				<text class="flex icon3 diygw-col-0 icon3-clz diy-icon-creative1"></text>
				<text class="diygw-col-0 text5-clz"> 意见反馈和帮助 </text>
			</view>
			<view class="flex flex-wrap diygw-col-24 items-center flex6-clz">
				<text class="flex icon2 diygw-col-0 icon2-clz diy-icon-comment"></text>
				<text class="diygw-col-0 text4-clz"> 消息订阅 </text>
			</view>
			<view class="flex flex-wrap diygw-col-24 items-center flex5-clz">
				<text class="flex icon1 diygw-col-0 icon1-clz diy-icon-starfill"></text>
				<text class="diygw-col-0 text3-clz"> 关于 </text>
			</view>
			<view class="flex flex-wrap diygw-col-24 items-center flex8-clz" @tap="navigateTo" data-type="logoutFunction">
				<text class="flex icon4 diygw-col-0 icon4-clz diy-icon-exit"></text>
				<text class="diygw-col-0 text6-clz"> 登出 </text>
			</view>
		</view>
		<view class="flex diygw-col-24 diygw-bottom">
			<view class="diygw-grid diygw-actions">
				<button @tap="navigateTo" data-type="page" data-url="/pages/home" class="diygw-action">
					<view class="diygw-grid-inner">
						<view class="diygw-grid-icon diygw-avatar" style="">
							<image mode="aspectFit" class="diygw-avatar-img" src="/static/global/grid1.png"></image>
						</view>
						<view class="diygw-grid-title"> 首页 </view>
					</view>
				</button>
				<button @tap="navigateTo" data-type="page" data-url="/pages/project/main_page" class="diygw-action">
					<view class="diygw-grid-inner">
						<view class="diygw-grid-icon diygw-avatar" style="">
							<image mode="aspectFit" class="diygw-avatar-img" src="/static/icon3_dd.png"></image>
						</view>
						<view class="diygw-grid-title"> 项目 </view>
					</view>
				</button>
				<button @tap="navigateTo" data-type="page" data-url="/pages/message/main_page" class="diygw-action">
					<view class="diygw-grid-inner">
						<view class="diygw-grid-icon diygw-avatar" style="">
							<image mode="aspectFit" class="diygw-avatar-img" src="/static/icon3_xx.png"></image>
						</view>
						<view class="diygw-grid-title"> 消息 </view>
					</view>
				</button>
				<button @tap="navigateTo" data-type="page" data-url="/pages/profile/main_page" class="diygw-action">
					<view class="diygw-grid-inner">
						<view class="diygw-grid-icon diygw-avatar" style="">
							<image mode="aspectFit" class="diygw-avatar-img" src="/static/icon1_rzyh.png"></image>
						</view>
						<view class="diygw-grid-title"> 我 </view>
					</view>
				</button>
			</view>
		</view>
		<view class="clearfix"></view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				//用户全局信息
				userInfo: {},
				//页面传参
				globalOption: {},
				//自定义全局变量
				globalData: { userDetail: { onboarding_year: '', college_category: { name: '' }, real_name: '', type: { name: '' }, specific_category: { name: '' } } }
			};
		},
		onShow() {
			this.setCurrentPage(this);

			this.initShow();
		},
		onLoad(option) {
			this.setCurrentPage(this);
			if (option) {
				this.setData({
					globalOption: this.getOption(option)
				});
			}

			this.init();
		},
		methods: {
			async init() {},
			async initShow() {
				await this.checkLoginFunction();
			},
			// 检查登录 自定义方法
			async checkLoginFunction(param) {
				let thiz = this;
				if (!this.$session.getToken()) {
					//比如未登录，转身到其他页面等
					this.showToast('请先登录');

					this.navigateTo({
						type: 'page',
						url: 'sign/login'
					});
				}

				this.globalData.userDetail = this.$session.getUser();
			},

			// 登出 自定义方法
			async logoutFunction(param) {
				let thiz = this;
				this.$session.clearUser();

				this.navigateTo({
					type: 'page',
					url: 'sign/login'
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.flex9-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.flex10-clz {
		border: 2rpx solid #aba6a6;
		border-bottom-left-radius: 12rpx;
		border-top-right-radius: 12rpx;
		margin-right: 10rpx;
		margin-left: 10rpx;
		flex-shrink: 0;
		overflow: hidden;
		width: calc(58.3333333333% - 10rpx - 10rpx) !important;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 10rpx;
		height: 178rpx !important;
	}
	.text7-clz {
		margin-left: 10rpx;
		font-size: 28rpx !important;
		margin-top: 0rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text8-clz {
		margin-left: 10rpx;
		font-size: 28rpx !important;
		margin-top: 0rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text9-clz {
		font-size: 28rpx !important;
	}
	.flex1-clz {
		border: 2rpx solid #aba6a6;
		border-bottom-left-radius: 12rpx;
		border-top-right-radius: 12rpx;
		margin-right: 10rpx;
		margin-left: 10rpx;
		flex-shrink: 0;
		overflow: hidden;
		width: calc(100% - 10rpx - 10rpx) !important;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 10rpx;
		height: 160rpx !important;
	}
	.flex2-clz {
		border-right: 2rpx solid #aba6a6;
	}
	.flex3-clz {
		border-left: 2rpx solid #aba6a6;
	}
	.flex4-clz {
		margin-left: 10rpx;
		border-bottom-left-radius: 12rpx;
		overflow: hidden;
		width: calc(100% - 10rpx - 10rpx) !important;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-top-right-radius: 12rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.icon-clz {
		margin-left: 10rpx;
		margin-top: 0rpx;
		margin-bottom: 0rpx;
		margin-right: 10rpx;
	}
	.icon {
		font-size: 50rpx;
	}
	.text2-clz {
		margin-left: 0rpx;
		font-size: 30rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.flex7-clz {
		margin-left: 10rpx;
		border-bottom-left-radius: 12rpx;
		overflow: hidden;
		width: calc(100% - 10rpx - 10rpx) !important;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-top-right-radius: 12rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.icon3-clz {
		margin-left: 10rpx;
		margin-top: 0rpx;
		margin-bottom: 0rpx;
		margin-right: 10rpx;
	}
	.icon3 {
		font-size: 50rpx;
	}
	.text5-clz {
		margin-left: 0rpx;
		font-size: 30rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.flex6-clz {
		margin-left: 10rpx;
		border-bottom-left-radius: 12rpx;
		overflow: hidden;
		width: calc(100% - 10rpx - 10rpx) !important;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-top-right-radius: 12rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.icon2-clz {
		margin-left: 10rpx;
		margin-top: 0rpx;
		margin-bottom: 0rpx;
		margin-right: 10rpx;
	}
	.icon2 {
		font-size: 50rpx;
	}
	.text4-clz {
		margin-left: 0rpx;
		font-size: 30rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.flex5-clz {
		margin-left: 10rpx;
		border-bottom-left-radius: 12rpx;
		overflow: hidden;
		width: calc(100% - 10rpx - 10rpx) !important;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-top-right-radius: 12rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.icon1-clz {
		margin-left: 10rpx;
		margin-top: 0rpx;
		margin-bottom: 0rpx;
		margin-right: 10rpx;
	}
	.icon1 {
		font-size: 50rpx;
	}
	.text3-clz {
		margin-left: 0rpx;
		font-size: 30rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.flex8-clz {
		margin-left: 10rpx;
		border-bottom-left-radius: 12rpx;
		overflow: hidden;
		width: calc(100% - 10rpx - 10rpx) !important;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-top-right-radius: 12rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.icon4-clz {
		margin-left: 10rpx;
		margin-top: 0rpx;
		margin-bottom: 0rpx;
		margin-right: 10rpx;
	}
	.icon4 {
		font-size: 50rpx;
	}
	.text6-clz {
		margin-left: 0rpx;
		font-size: 30rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.container328924 {
		padding-bottom: 80px;
	}
</style>
