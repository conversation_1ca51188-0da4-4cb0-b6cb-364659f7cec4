<template>
	<view class="container container328924">
		<view class="flex diygw-col-24 diygw-bottom">
			<view class="diygw-grid diygw-actions">
				<button @tap="navigateTo" data-type="page" data-url="/pages/home" class="diygw-action">
					<view class="diygw-grid-inner">
						<view class="diygw-grid-icon diygw-avatar" style="">
							<image mode="aspectFit" class="diygw-avatar-img" src="/static/global/grid1.png"></image>
						</view>
						<view class="diygw-grid-title"> 首页 </view>
					</view>
				</button>
				<button @tap="navigateTo" data-type="page" data-url="/pages/project/main_page" class="diygw-action">
					<view class="diygw-grid-inner">
						<view class="diygw-grid-icon diygw-avatar" style="">
							<image mode="aspectFit" class="diygw-avatar-img" src="/static/icon3_dd.png"></image>
						</view>
						<view class="diygw-grid-title"> 项目 </view>
					</view>
				</button>
				<button @tap="navigateTo" data-type="page" data-url="/pages/message/main_page" class="diygw-action">
					<view class="diygw-grid-inner">
						<view class="diygw-grid-icon diygw-avatar" style="">
							<image mode="aspectFit" class="diygw-avatar-img" src="/static/icon3_xx.png"></image>
						</view>
						<view class="diygw-grid-title"> 消息 </view>
					</view>
				</button>
				<button @tap="navigateTo" data-type="page" data-url="/pages/profile/main_page" class="diygw-action">
					<view class="diygw-grid-inner">
						<view class="diygw-grid-icon diygw-avatar" style="">
							<image mode="aspectFit" class="diygw-avatar-img" src="/static/icon1_rzyh.png"></image>
						</view>
						<view class="diygw-grid-title"> 我 </view>
					</view>
				</button>
			</view>
		</view>
		<view v-for="(item, index) in globalData.newsList" :key="index" class="flex flex-wrap diygw-col-24" @tap="navigateTo" data-type="page" data-url="/pages/news/detail" :data-id="item._id">
			<image :src="item.avatar.url" class="image4-size diygw-image diygw-col-0 image4-clz" mode="heightFix"></image>
			<view class="flex flex-wrap diygw-col-0 flex-direction-column justify-between flex11-clz">
				<rich-text :nodes="item.title" class="diygw-col-0"></rich-text>
				<text class="diygw-col-0 text10-clz">
					{{ $tools.formatDateTime(item.create_time, 'HH:MM YYYY-mm-dd') }}
				</text>
			</view>
		</view>
		<view class="clearfix"></view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				//用户全局信息
				userInfo: {},
				//页面传参
				globalOption: {},
				//自定义全局变量
				globalData: { newList: [] },
				text9: `<p><span style="font-size: 14px;">运营机构要切实履行数据安全的主体责任</span></p>`
			};
		},
		onShow() {
			this.setCurrentPage(this);
		},
		onLoad(option) {
			this.setCurrentPage(this);
			if (option) {
				this.setData({
					globalOption: this.getOption(option)
				});
			}

			this.init();
		},
		methods: {
			async init() {
				await this.getListFunction();
			},
			// 获取新闻列表 自定义方法
			async getListFunction(param) {
				let thiz = this;
				const res = await uniCloud.importObject('News').getList(8);
				this.globalData.newsList = res.data;
			}
		}
	};
</script>

<style lang="scss" scoped>
	.image4-clz {
		margin-left: 10rpx;
		flex-shrink: 0;
		border-bottom-left-radius: 12rpx;
		overflow: hidden;
		width: 166rpx !important;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-top-right-radius: 12rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 10rpx;
		height: 136rpx !important;
		margin-right: 0rpx;
	}
	.image4-size {
		height: 138rpx !important;
		width: 166rpx !important;
	}
	.flex11-clz {
		margin-left: 10rpx;
		flex: 1;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text10-clz {
		direction: rtl;
	}
	.container328924 {
		padding-bottom: 80px;
	}
</style>
