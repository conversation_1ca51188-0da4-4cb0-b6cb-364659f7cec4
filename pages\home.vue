<template>
	<view class="container container328924">
		<view class="flex flex-wrap diygw-col-24 flex-direction-column flex6-clz phone-container">
			<view class="flex flex-wrap diygw-col-24 justify-between">
				<text class="diygw-col-0 diygw-text-lg text-blue"> XMU </text>
			</view>
			<view class="flex diygw-col-24">
				<swiper :current="swiperIndex" class="swiper" @change="changeSwiper" indicator-color="rgba(51, 51, 51, 0.39)" indicator-active-color="#fff" autoplay interval="3000" circular="true" style="height: 300rpx">
					<swiper-item class="diygw-swiper-item">
						<view class="diygw-swiper-item-wrap">
							<image src="/static/pic1.jpg" class="diygw-swiper-image"></image>
						</view>
					</swiper-item>
					<swiper-item class="diygw-swiper-item">
						<view class="diygw-swiper-item-wrap">
							<image src="/static/pic2.jpg" class="diygw-swiper-image"></image>
						</view>
					</swiper-item>
					<swiper-item class="diygw-swiper-item">
						<view class="diygw-swiper-item-wrap">
							<image src="/static/pic3.jpg" class="diygw-swiper-image"></image>
						</view>
					</swiper-item>
				</swiper>
			</view>
			<view class="flex flex-wrap diygw-col-24 flex-direction-column">
				<text class="diygw-col-24"> 最新情报（教务处上传） </text>
				<view v-for="(item, index) in globalData.newsList" :key="index" class="flex flex-wrap diygw-col-24" @tap="navigateTo" data-type="page" data-url="/pages/news/detail" :data-id="item._id">
					<image :src="item.avatar.url" class="image4-size diygw-image diygw-col-0 image4-clz" mode="heightFix"></image>
					<view class="flex flex-wrap diygw-col-0 flex-direction-row-reverse justify-end flex11-clz">
						<rich-text :nodes="item.title" class="diygw-col-0"></rich-text>
						<text class="diygw-col-0 text10-clz">
							{{ $tools.formatDateTime(item.create_time, 'HH:MM YYYY-mm-dd') }}
						</text>
					</view>
				</view>
				<view class="flex flex-wrap diygw-col-24 flex-direction-row-reverse items-center flex2-clz" @tap="navigateTo" data-type="page" data-url="/pages/news/list">
					<text class="flex icon diygw-col-0 diy-icon-right"></text>
					<text class="diygw-col-0"> 更多 </text>
				</view>
			</view>
			<text class="diygw-col-24"> 最新/最热竞赛 </text>
			<view class="flex flex-wrap diygw-col-24 items-start">
				<view v-for="(item, index) in globalData.competitionList" :key="index" class="flex flex-wrap diygw-col-24 flex-direction-column justify-between flex47-clz" @tap="navigateTo" data-type="page" data-url="/pages/competition/detail" :data-id="item._id">
					<text class="diygw-text-line4 diygw-col-24 text43-clz">
						{{ item.title }}
					</text>
					<view class="flex flex-wrap diygw-col-0 justify-between flex48-clz">
						<text class="diygw-col-24 text42-clz">
							{{ $tools.formatDateTime(item.create_time, 'YYYY-mm-dd HH:MM') }}
						</text>
					</view>
				</view>
			</view>
			<view class="flex flex-wrap diygw-col-24 flex-direction-row-reverse items-center flex4-clz" @tap="navigateTo" data-type="page" data-url="/pages/competition/list">
				<text class="flex icon1 diygw-col-0 diy-icon-right"></text>
				<text class="diygw-col-0"> 更多 </text>
			</view>
			<view class="flex diygw-col-24 diygw-bottom">
				<view class="diygw-grid diygw-actions">
					<button @tap="navigateTo" data-type="page" data-url="/pages/home" class="diygw-action">
						<view class="diygw-grid-inner">
							<view class="diygw-grid-icon diygw-avatar" style="">
								<image mode="aspectFit" class="diygw-avatar-img" src="/static/global/grid1.png"></image>
							</view>
							<view class="diygw-grid-title"> 首页 </view>
						</view>
					</button>
					<button @tap="navigateTo" data-type="page" data-url="/pages/project/main_page" class="diygw-action">
						<view class="diygw-grid-inner">
							<view class="diygw-grid-icon diygw-avatar" style="">
								<image mode="aspectFit" class="diygw-avatar-img" src="/static/icon3_dd.png"></image>
							</view>
							<view class="diygw-grid-title"> 项目 </view>
						</view>
					</button>
					<button @tap="navigateTo" data-type="page" data-url="/pages/message/main_page" class="diygw-action">
						<view class="diygw-grid-inner">
							<view class="diygw-grid-icon diygw-avatar" style="">
								<image mode="aspectFit" class="diygw-avatar-img" src="/static/icon3_xx.png"></image>
							</view>
							<view class="diygw-grid-title"> 消息 </view>
						</view>
					</button>
					<button @tap="navigateTo" data-type="page" data-url="/pages/profile/main_page" class="diygw-action">
						<view class="diygw-grid-inner">
							<view class="diygw-grid-icon diygw-avatar" style="">
								<image mode="aspectFit" class="diygw-avatar-img" src="/static/icon1_rzyh.png"></image>
							</view>
							<view class="diygw-grid-title"> 我 </view>
						</view>
					</button>
				</view>
			</view>
			<view class="flex flex-wrap diygw-col-24 flex-direction-column" @tap="navigateTo" data-type="page" data-url="/pages/user/list">
				<view class="flex diygw-col-24 justify-center">
					<view class="diygw-avatar lg margin-md radius-xs bg-none">
						<image mode="aspectFit" class="diygw-avatar-img radius-xs" src="/static/icon2_xm.png"></image>
					</view>
				</view>
				<text class="diygw-col-24 text5-clz diygw-text-lg"> 查找用户 </text>
			</view>
		</view>
		<view class="clearfix"></view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				//用户全局信息
				userInfo: {},
				//页面传参
				globalOption: {},
				//自定义全局变量
				globalData: { newsList: [], competitionList: [] },
				swiperIndex: 0,
				text9: `<p><span style="font-size: 14px;">运营机构要切实履行数据安全的主体责任</span></p>`
			};
		},
		onShow() {
			this.setCurrentPage(this);
		},
		onLoad(option) {
			this.setCurrentPage(this);
			if (option) {
				this.setData({
					globalOption: this.getOption(option)
				});
			}

			this.init();
		},
		methods: {
			async init() {
				this.getNewsListFunction();
				await this.getCompetitionListFunction();
			},
			// 获取新闻 自定义方法
			async getNewsListFunction(param) {
				let thiz = this;
				const res = await uniCloud.importObject('News').getList(3);
				this.globalData.newsList = res.data;
			},

			// 获取竞赛 自定义方法
			async getCompetitionListFunction(param) {
				let thiz = this;
				const res = await uniCloud.importObject('Competition').getList(3, 0);

				if (res.status == 0) {
					uni.showToast({
						title: res.msg,
						icon: 'error',
						duration: 2000
					});

					return;
				}

				this.globalData.competitionList = res.data;
			},
			changeSwiper(evt) {
				let swiperIndex = evt.detail.current;
				this.setData({ swiperIndex });
			}
		}
	};
</script>

<style lang="scss" scoped>
	.flex6-clz {
		height: 100%;
	}
	.swiper-title {
		background-color: rgba(0, 0, 0, 0.281);
	}
	.image4-clz {
		margin-left: 10rpx;
		flex-shrink: 0;
		border-bottom-left-radius: 12rpx;
		overflow: hidden;
		width: 166rpx !important;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-top-right-radius: 12rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 10rpx;
		height: 136rpx !important;
		margin-right: 0rpx;
	}
	.image4-size {
		height: 138rpx !important;
		width: 166rpx !important;
	}
	.flex11-clz {
		margin-left: 10rpx;
		flex: 1;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text10-clz {
		direction: rtl;
	}
	.flex2-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		margin-top: 0rpx;
		margin-bottom: 10rpx;
		text-align: right;
		margin-right: 10rpx;
	}
	.icon {
		font-size: 30rpx;
	}
	.flex47-clz {
		margin-left: 0rpx;
		flex-shrink: 0;
		width: calc(100% - 0rpx - 0rpx) !important;
		margin-top: 12rpx;
		margin-bottom: 10rpx;
		margin-right: 0rpx;
	}
	.text43-clz {
		margin-left: 0rpx;
		color: #050505;
		width: calc(100% - 0rpx - 0rpx) !important;
		font-size: 26rpx !important;
		margin-top: 2rpx;
		margin-bottom: 20rpx;
		margin-right: 0rpx;
	}
	.flex48-clz {
		margin-left: 0rpx;
		margin-top: 0rpx;
		margin-bottom: -2rpx;
		margin-right: 0rpx;
	}
	.text42-clz {
		margin-left: 0rpx;
		color: #756f6f;
		width: calc(100% - 0rpx - 10rpx) !important;
		font-size: 26rpx !important;
		margin-top: 0rpx;
		margin-bottom: 0rpx;
		text-align: left;
		margin-right: 10rpx;
	}
	.flex4-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		margin-top: 0rpx;
		margin-bottom: 10rpx;
		text-align: right;
		margin-right: 10rpx;
	}
	.icon1 {
		font-size: 30rpx;
	}
	.text5-clz {
		text-align: center;
	}
	.container328924 {
		padding-bottom: 80px;
	}
</style>
