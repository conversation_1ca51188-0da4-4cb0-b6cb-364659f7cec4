{"bsonType": "object", "required": ["project_id", "college_category_id"], "permission": {"read": false, "create": false, "update": false, "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}, "project_id": {"bsonType": "string", "title": "项目ID", "description": "项目ID", "foreignKey": "xm-stp-project._id"}, "college_category_id": {"bsonType": "string", "title": "学院类别ID", "description": "学院类别ID", "foreignKey": "xm-stp-college_cat._id"}, "specific_category_id": {"bsonType": "string", "title": "专业类别ID", "description": "专业类别ID", "foreignKey": "xm-stp-specific_cat._id"}}}