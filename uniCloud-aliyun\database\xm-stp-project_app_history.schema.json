{"bsonType": "object", "required": ["user_id", "project_id", "action"], "permission": {"read": false, "create": false, "update": false, "delete": false}, "properties": {"_id": {"description": "请求历史记录ID，系统自动生成"}, "user_id": {"bsonType": "string", "title": "用户ID", "description": "用户ID", "foreignKey": "xm-stp-user._id"}, "project_id": {"bsonType": "string", "title": "项目ID", "description": "项目ID", "foreignKey": "xm-stp-project_detail._id"}, "action": {"bsonType": "int", "title": "操作", "description": "操作类型，【0：申请加入，1：退出项目申请，2：发出邀请，3：拒绝邀请，4：加入候选，5：退出候选】"}, "create_time": {"bsonType": "timestamp", "title": "创建时间", "description": "创建时间", "forceDefaultValue": {"$env": "now"}}}}