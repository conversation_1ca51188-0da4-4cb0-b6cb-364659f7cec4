<template>
	<view class="container container328924">
		<view class="flex diygw-col-24">
			<view class="diygw-grid col-2">
				<view class="diygw-grid-item">
					<view class="diygw-grid-inner">
						<view class="diygw-grid-icon diygw-avatar">
							<image mode="aspectFit" class="diygw-avatar-img" src="/static/global/grid3.png"></image>
						</view>
						<view class="diygw-grid-title"> 被邀请 </view>
					</view>
				</view>
				<view class="diygw-grid-item">
					<view class="diygw-grid-inner">
						<view class="diygw-grid-icon diygw-avatar">
							<image mode="aspectFit" class="diygw-avatar-img" src="/static/global/grid3.png"></image>
						</view>
						<view class="diygw-grid-title"> 待加入 </view>
					</view>
				</view>
			</view>
		</view>
		<view v-if="globalData.invite_list.length" class="flex flex-wrap diygw-col-24 flex-direction-column flex16-clz">
			<text class="diygw-col-24"> 他人项目邀请列表 </text>
			<view v-for="(item, index) in globalData.invite_list" :key="index" class="flex flex-wrap diygw-col-0 flex-direction-column justify-center flex24-clz">
				<text @tap="navigateTo" data-type="page" data-url="/pages/project/detail/others" :data-id="item.project_id" class="diygw-col-0 text24-clz">
					{{ item.title }}
				</text>
				<text @tap="navigateTo" data-type="page" data-url="/pages/project/detail/others" :data-id="item.project_id" class="diygw-col-0 text3-clz">
					{{ $tools.formatDateTime(item.create_time, 'YYYY-mm-dd HH:MM') }}
				</text>
				<view class="flex diygw-col-24">
					<button @tap="navigateTo" data-type="openInviteModalFunction" :data-detail="item" :data-index="index" class="diygw-btn cyan radius-xs flex-sub margin-xs button-button-clz">邀请函</button>
				</view>
			</view>
		</view>
		<view v-if="globalData.request_list.length" class="flex flex-wrap diygw-col-24 flex-direction-column flex-clz">
			<text class="diygw-col-24"> 申请列表（我的项目） </text>
			<view v-for="(item, index) in globalData.request_list" :key="index" class="flex flex-wrap diygw-col-0 flex-direction-column justify-center flex3-clz">
				<view class="flex flex-wrap diygw-col-24 justify-between items-center flex4-clz">
					<text class="diygw-col-0">
						{{ item.title }}
					</text>
				</view>
				<text class="diygw-col-0 text4-clz"> 申请人数： {{ item.current_person_request ?? 0 }} </text>
			</view>
		</view>
		<view class="flex diygw-col-24 diygw-bottom">
			<view class="diygw-grid diygw-actions">
				<button @tap="navigateTo" data-type="page" data-url="/pages/home" class="diygw-action">
					<view class="diygw-grid-inner">
						<view class="diygw-grid-icon diygw-avatar" style="">
							<image mode="aspectFit" class="diygw-avatar-img" src="/static/global/grid1.png"></image>
						</view>
						<view class="diygw-grid-title"> 首页 </view>
					</view>
				</button>
				<button @tap="navigateTo" data-type="page" data-url="/pages/project/main_page" class="diygw-action">
					<view class="diygw-grid-inner">
						<view class="diygw-grid-icon diygw-avatar" style="">
							<image mode="aspectFit" class="diygw-avatar-img" src="/static/icon3_dd.png"></image>
						</view>
						<view class="diygw-grid-title"> 项目 </view>
					</view>
				</button>
				<button @tap="navigateTo" data-type="page" data-url="/pages/message/main_page" class="diygw-action">
					<view class="diygw-grid-inner">
						<view class="diygw-grid-icon diygw-avatar" style="">
							<image mode="aspectFit" class="diygw-avatar-img" src="/static/icon3_xx.png"></image>
						</view>
						<view class="diygw-grid-title"> 消息 </view>
					</view>
				</button>
				<button @tap="navigateTo" data-type="page" data-url="/pages/profile/main_page" class="diygw-action">
					<view class="diygw-grid-inner">
						<view class="diygw-grid-icon diygw-avatar" style="">
							<image mode="aspectFit" class="diygw-avatar-img" src="/static/icon1_rzyh.png"></image>
						</view>
						<view class="diygw-grid-title"> 我 </view>
					</view>
				</button>
			</view>
		</view>
		<view @touchmove.stop.prevent="" v-if="modal" class="diygw-modal basic" :class="modal" style="z-index: 1000000">
			<view class="diygw-dialog diygw-dialog-modal basis-lg">
				<view class="justify-end diygw-bar">
					<view class="content">
						{{ globalData.inviteShow.title }}
					</view>
					<view class="action" data-type="closemodal" data-id="modal" @tap="navigateTo">
						<text class="diy-icon-close"></text>
					</view>
				</view>
				<view>
					<view class="flex diygw-dialog-content">
						<text class="diygw-col-24 text5-clz">
							{{ globalData.inviteShow.comment }}
						</text>
						<view class="flex diygw-col-24">
							<button @tap="navigateTo" data-type="declineInviteFunction" class="diygw-btn red radius-xs flex-sub margin-xs button1-button-clz">拒绝</button>
							<button @tap="navigateTo" data-type="approveInviteFunction" class="diygw-btn green radius-xs flex-sub margin-xs button1-button-clz">加入</button>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view v-for="(item, index) in globalData.newsList" :key="index" class="flex flex-wrap diygw-col-24" @tap="navigateTo" data-type="page" data-url="/pages/news/detail" :data-id="item._id">
			<image :src="item.avatar.url" class="image4-size diygw-image diygw-col-0 image4-clz" mode="heightFix"></image>
			<view class="flex flex-wrap diygw-col-0 flex-direction-row-reverse justify-end flex11-clz">
				<rich-text :nodes="item.title" class="diygw-col-0"></rich-text>
				<text class="diygw-col-0 text10-clz">
					{{ $tools.formatDateTime(item.create_time, 'HH:MM YYYY-mm-dd') }}
				</text>
			</view>
		</view>
		<view class="clearfix"></view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				//用户全局信息
				userInfo: {},
				//页面传参
				globalOption: {},
				//自定义全局变量
				globalData: { request_list: [], invite_list: [], inviteShow: {} },
				modal: '',
				text9: `<p><span style="font-size: 14px;">运营机构要切实履行数据安全的主体责任</span></p>`
			};
		},
		onShow() {
			this.setCurrentPage(this);
		},
		onLoad(option) {
			this.setCurrentPage(this);
			if (option) {
				this.setData({
					globalOption: this.getOption(option)
				});
			}

			this.init();
		},
		methods: {
			async init() {
				await this.initializeFunction();
				await this.checkLoginStateFunction();
			},
			// 获取页面的所有信息 自定义方法
			async initializeFunction(param) {
				let thiz = this;
				const res = await uniCloud.importObject('ProjectAction').getListFromMsg({ user_id: this.$session.getUserValue('user_id') });

				this.globalData.request_list = res.data.request_list;
				this.globalData.invite_list = res.data.invite_list;
			},

			// 检查是否登录 自定义方法
			async checkLoginStateFunction(param) {
				let thiz = this;
				if (!this.$session.getToken()) {
					//比如未登录，转身到其他页面等
					this.showToast('请先登录');

					this.navigateTo({
						type: 'page',
						url: 'sign/login'
					});
				}
			},

			// 开启邀请函窗口 自定义方法
			async openInviteModalFunction(param) {
				let thiz = this;
				this.navigateTo({
					type: 'openmodal',
					id: 'modal'
				});

				this.globalData.inviteShow = param.detail;
				this.globalData.inviteIndex = param.index;
			},

			// 拒绝邀请 自定义方法
			async declineInviteFunction(param) {
				let thiz = this;
				const res = await uniCloud.importObject('ProjectAction').declineJoin({ user_id: this.$session.getUserValue('user_id'), project_id: this.globalData.inviteShow.project_id });

				uni.showToast({
					title: res.msg,
					icon: res.status ? 'success' : 'error'
				});

				if (res.status == 0) return;

				this.globalData.invite_list.splice(this.globalData.inviteIndex, 1);
				this.globalData.inviteIndex = null;

				this.navigateTo({
					type: 'closemodal',
					id: 'modal'
				});
			},

			// 接受邀请 自定义方法
			async approveInviteFunction(param) {
				let thiz = this;
				const res = await uniCloud.importObject('ProjectAction').approveJoin({ user_id: this.$session.getUserValue('user_id'), project_id: this.globalData.inviteShow.project_id });

				uni.showToast({
					title: res.msg,
					icon: res.status ? 'success' : 'error'
				});

				if (res.status == 0) return;

				this.globalData.invite_list.splice(this.globalData.inviteIndex, 1);
				this.globalData.inviteIndex = null;

				this.navigateTo({
					type: 'closemodal',
					id: 'modal'
				});
			}
		}
	};
</script>

<style lang="scss" scoped>
	.flex16-clz {
		padding-top: 10rpx;
		border-bottom-left-radius: 12rpx;
		padding-left: 10rpx;
		padding-bottom: 10rpx;
		border-top-right-radius: 12rpx;
		margin-right: 10rpx;
		background-color: #ffffff;
		margin-left: 10rpx;
		overflow: hidden;
		width: calc(100% - 10rpx - 10rpx) !important;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 10rpx;
		padding-right: 10rpx;
	}
	.flex24-clz {
		flex: 1;
		border-bottom: 2rpx solid #eee;
	}
	.text24-clz {
		padding-top: 10rpx;
		flex: 1;
		padding-left: 10rpx;
		padding-bottom: 10rpx;
		padding-right: 10rpx;
	}
	.text3-clz {
		padding-top: 10rpx;
		flex: 1;
		padding-left: 10rpx;
		padding-bottom: 10rpx;
		padding-right: 10rpx;
	}
	.button-button-clz {
		margin: 6rpx !important;
	}
	.flex-clz {
		padding-top: 10rpx;
		border-bottom-left-radius: 12rpx;
		padding-left: 10rpx;
		padding-bottom: 10rpx;
		border-top-right-radius: 12rpx;
		margin-right: 10rpx;
		background-color: #ffffff;
		margin-left: 10rpx;
		overflow: hidden;
		width: calc(100% - 10rpx - 10rpx) !important;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 10rpx;
		padding-right: 10rpx;
	}
	.flex3-clz {
		flex: 1;
		border-bottom: 2rpx solid #eee;
	}
	.flex4-clz {
		padding-top: 10rpx;
		color: #9d9d9d;
		flex: 1;
		padding-left: 10rpx;
		padding-bottom: 10rpx;
		padding-right: 10rpx;
	}
	.text4-clz {
		padding-top: 10rpx;
		flex: 1;
		padding-left: 10rpx;
		padding-bottom: 10rpx;
		padding-right: 10rpx;
	}
	.modal-clz {
		z-index: 1000000;
	}
	.diygw-dialog-modal {
	}
	.text5-clz {
		font-size: 32rpx !important;
		text-align: center;
	}
	.button1-button-clz {
		margin: 6rpx !important;
	}
	.image4-clz {
		margin-left: 10rpx;
		flex-shrink: 0;
		border-bottom-left-radius: 12rpx;
		overflow: hidden;
		width: 166rpx !important;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-top-right-radius: 12rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 10rpx;
		height: 136rpx !important;
		margin-right: 0rpx;
	}
	.image4-size {
		height: 138rpx !important;
		width: 166rpx !important;
	}
	.flex11-clz {
		margin-left: 10rpx;
		flex: 1;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text10-clz {
		direction: rtl;
	}
	.container328924 {
		padding-bottom: 80px;
	}
</style>
