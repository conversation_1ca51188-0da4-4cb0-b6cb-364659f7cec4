<template>
	<view class="container container328924">
		<view v-for="(item, index) in globalData.list" :key="index" class="flex flex-wrap diygw-col-24 flex-direction-column justify-center items-center">
			<view class="flex flex-wrap diygw-col-24 flex5-clz">
				<view class="flex flex-wrap diygw-col-16 flex-direction-column">
					<text class="diygw-col-0 text1-clz">
						{{ item.title }}
					</text>
					<text class="diygw-col-0 text2-clz"> 项目类型：{{ item.type }} </text>
					<text v-if="item.comp" class="diygw-col-0 text8-clz"> 竞赛：{{ item.comp }} </text>
					<text v-if="item.comp_name" class="diygw-col-0 text-clz"> 竞赛：{{ item.comp_name }} </text>
					<text class="diygw-col-24 text3-clz"> 申请者人数要求： {{ item.person_needed }} 人 </text>
					<text class="diygw-col-24 text4-clz"> 申请加入者： {{ item.person_request ?? 0 }} 人 </text>
					<text class="diygw-col-24 text6-clz"> 创建时间： {{ $tools.formatDateTime(item.create_time, 'YYYY-mm-dd HH:MM') }} </text>
					<text class="diygw-col-24 text7-clz"> 结束时间： {{ endingDateReturnFunction({ ending_time: item.ending_time }) }} </text>
				</view>
				<view class="flex flex-wrap diygw-col-0 items-start flex4-clz">
					<view class="flex flex-wrap diygw-col-24 flex-direction-row-reverse">
						<text class="diygw-col-0 text5-clz">
							{{ item.status }}
						</text>
					</view>
					<view class="flex flex-wrap diygw-col-24 flex-direction-row-reverse flex15-clz">
						<text @tap="navigateTo" data-type="page" data-url="/pages/project/edit" :data-id="item._id" class="diygw-col-0 text26-clz"> 更新 </text>
						<text @tap="navigateTo" data-type="page" data-url="/pages/project/detail/self" :data-id="item._id" class="diygw-col-0 text11-clz"> 查看 </text>
					</view>
				</view>
			</view>
		</view>
		<view class="clearfix"></view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				//用户全局信息
				userInfo: {},
				//页面传参
				globalOption: {},
				//自定义全局变量
				globalData: { list: [] }
			};
		},
		onShow() {
			this.setCurrentPage(this);

			this.initShow();
		},
		onLoad(option) {
			this.setCurrentPage(this);
			if (option) {
				this.setData({
					globalOption: this.getOption(option)
				});
			}

			this.init();
		},
		methods: {
			async init() {
				await this.getListFunction();
			},
			async initShow() {
				await this.checkLoginFunction();
			},
			// 检查登录 自定义方法
			async checkLoginFunction(param) {
				let thiz = this;
				if (!this.$session.getToken()) {
					//比如未登录，转身到其他页面等
					this.showToast('请先登录');

					this.navigateTo({
						type: 'page',
						url: 'sign/login'
					});
				}
			},

			// 获取自身的项目列表 自定义方法
			async getListFunction(param) {
				let thiz = this;
				const res = await uniCloud.importObject('Project').getSelf({ user_id: this.$session.getUserValue('user_id') });

				if (res.status == 0) {
					uni.showToast({
						title: res.msg,
						icon: 'error',
						duration: 2000
					});
					setTimeout(function () {
						uni.redirectTo({
							url: '/pages/home'
						});
					}, 2000);
				}

				this.globalData.list = res.data;
			},

			// 返回时间和是否过期 自定义方法
			endingDateReturnFunction(param) {
				let thiz = this;
				let ending_time = param && (param.ending_time || param.ending_time == 0) ? param.ending_time : '';
				var date = this.$tools.formatDateTime(ending_time, 'YYYY-mm-dd HH:MM');
				date += this.$tools.formatDateTime(ending_time, 'YYYY-mm-dd HH:MM') < this.$tools.getCurrentDateTime() ? '(已过期)' : '';
				return date;
			}
		}
	};
</script>

<style lang="scss" scoped>
	.flex5-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text1-clz {
		margin-left: 10rpx;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text2-clz {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text8-clz {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text-clz {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text3-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text4-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text6-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text7-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.flex4-clz {
		flex: 1;
	}
	.text5-clz {
		padding-top: 2rpx;
		border-bottom-left-radius: 12rpx;
		padding-left: 10rpx;
		padding-bottom: 10rpx;
		border-top-right-radius: 12rpx;
		margin-right: 10rpx;
		background-color: #e5eae5;
		margin-left: 10rpx;
		flex-shrink: 0;
		overflow: hidden;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 10rpx;
		height: 40rpx !important;
		padding-right: 10rpx;
	}
	.flex15-clz {
		margin-left: 2rpx;
		flex-shrink: 0;
		left: 0rpx;
		bottom: 0rpx;
		width: calc(100% - 2rpx - 0rpx) !important;
		margin-top: 0rpx;
		position: absolute;
		margin-bottom: 6rpx;
		height: 60rpx !important;
		margin-right: 0rpx;
	}
	.text26-clz {
		margin-left: 10rpx;
		border: 2rpx solid #aba6a6;
		flex-shrink: 0;
		padding-top: 4rpx;
		padding-left: 20rpx;
		width: 96rpx !important;
		padding-bottom: 4rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		height: 46rpx !important;
		margin-right: 10rpx;
		padding-right: 12rpx;
	}
	.text11-clz {
		margin-left: 10rpx;
		border: 2rpx solid #aba6a6;
		flex-shrink: 0;
		padding-top: 4rpx;
		padding-left: 20rpx;
		width: 96rpx !important;
		padding-bottom: 4rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		height: 46rpx !important;
		margin-right: 10rpx;
		padding-right: 10rpx;
	}
	.container328924 {
	}
</style>
