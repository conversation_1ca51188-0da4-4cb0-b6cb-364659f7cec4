<template>
	<view class="container container328924">
		<view class="flex flex-wrap diygw-col-24 flex-direction-column justify-center items-center">
			<view class="flex diygw-col-0 avatar-clz">
				<view class="diygw-avatar xl radius bg-none">
					<image mode="aspectFit" class="diygw-avatar-img radius" src="/static/team-3.jpg"></image>
				</view>
			</view>
			<text class="diygw-col-0 text-clz"> 某某某 </text>
		</view>
		<view class="flex flex-wrap diygw-col-24 flex-direction-column">
			<view class="flex flex-wrap diygw-col-24 flex5-clz">
				<view class="flex flex-wrap diygw-col-15 flex-direction-column flex3-clz" @tap="navigateTo" data-type="openmodal">
					<text class="diygw-col-0 text1-clz"> 项目名 </text>
					<text class="diygw-col-0 text2-clz"> 项目类型 </text>
					<text class="diygw-col-0 text3-clz"> 申请人数： 8 人 </text>
					<text class="diygw-col-0 text4-clz"> 已加入： 20 / 6 </text>
					<text class="diygw-col-24 text6-clz"> 创建时间： YYYY/MM/DD </text>
				</view>
				<view class="flex flex-wrap diygw-col-5">
					<view class="flex flex-wrap diygw-col-24 flex-direction-row-reverse flex14-clz">
						<text class="diygw-col-0 text5-clz"> 项目状态 </text>
					</view>
					<view class="flex flex-wrap diygw-col-24 flex-direction-row-reverse flex15-clz">
						<text class="diygw-col-0 text26-clz"> 加入 </text>
					</view>
				</view>
			</view>
			<view class="flex flex-wrap diygw-col-24 flex10-clz">
				<view class="flex flex-wrap diygw-col-15 flex-direction-column flex11-clz" @tap="navigateTo" data-type="openmodal">
					<text class="diygw-col-0 text14-clz"> 项目名 </text>
					<text class="diygw-col-0 text15-clz"> 项目类型 </text>
					<text class="diygw-col-0 text16-clz"> 申请人数： 8 人 </text>
					<text class="diygw-col-0 text17-clz"> 已加入： 20 / 6 </text>
					<text class="diygw-col-24 text18-clz"> 创建时间： YYYY/MM/DD </text>
				</view>
				<view class="flex flex-wrap diygw-col-5">
					<view class="flex flex-wrap diygw-col-24 flex-direction-row-reverse flex13-clz">
						<text class="diygw-col-0 text19-clz"> 项目状态 </text>
					</view>
					<view class="flex flex-wrap diygw-col-24 flex-direction-row-reverse flex16-clz">
						<text class="diygw-col-0 text20-clz"> 加入 </text>
					</view>
				</view>
			</view>
			<view class="flex flex-wrap diygw-col-24 flex2-clz">
				<view class="flex flex-wrap diygw-col-15 flex-direction-column flex6-clz" @tap="navigateTo" data-type="openmodal">
					<text class="diygw-col-0 text7-clz"> 项目名 </text>
					<text class="diygw-col-0 text8-clz"> 项目类型 </text>
					<text class="diygw-col-0 text9-clz"> 申请人数： 8 人 </text>
					<text class="diygw-col-0 text10-clz"> 已加入： 20 / 6 </text>
					<text class="diygw-col-24 text11-clz"> 创建时间： YYYY/MM/DD </text>
				</view>
				<view class="flex flex-wrap diygw-col-5">
					<view class="flex flex-wrap diygw-col-24 flex-direction-row-reverse flex8-clz">
						<text class="diygw-col-0 text12-clz"> 项目状态 </text>
					</view>
					<view class="flex flex-wrap diygw-col-24 flex-direction-row-reverse flex9-clz">
						<text class="diygw-col-0 text13-clz"> 加入 </text>
					</view>
				</view>
			</view>
		</view>
		<view class="clearfix"></view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				//用户全局信息
				userInfo: {},
				//页面传参
				globalOption: {},
				//自定义全局变量
				globalData: {}
			};
		},
		onShow() {
			this.setCurrentPage(this);
		},
		onLoad(option) {
			this.setCurrentPage(this);
			if (option) {
				this.setData({
					globalOption: this.getOption(option)
				});
			}

			this.init();
		},
		methods: {
			async init() {}
		}
	};
</script>

<style lang="scss" scoped>
	.avatar-clz {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 30rpx;
		margin-right: 10rpx;
	}
	.text-clz {
		font-size: 26rpx !important;
	}
	.flex5-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.flex3-clz {
		flex: 1;
	}
	.text1-clz {
		margin-left: 10rpx;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text2-clz {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text3-clz {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text4-clz {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text6-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.flex14-clz {
		flex-shrink: 0;
		height: 200rpx !important;
	}
	.text5-clz {
		padding-top: 2rpx;
		border-bottom-left-radius: 12rpx;
		padding-left: 10rpx;
		padding-bottom: 10rpx;
		border-top-right-radius: 12rpx;
		margin-right: 10rpx;
		background-color: #e5eae5;
		margin-left: 10rpx;
		flex-shrink: 0;
		overflow: hidden;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 10rpx;
		height: 40rpx !important;
		padding-right: 10rpx;
	}
	.flex15-clz {
		flex-shrink: 0;
		height: 60rpx !important;
	}
	.text26-clz {
		margin-left: 10rpx;
		border: 2rpx solid #aba6a6;
		padding-top: 2rpx;
		padding-left: 10rpx;
		padding-bottom: 4rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
		padding-right: 10rpx;
	}
	.flex10-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.flex11-clz {
		flex: 1;
	}
	.text14-clz {
		margin-left: 10rpx;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text15-clz {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text16-clz {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text17-clz {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text18-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.flex13-clz {
		flex-shrink: 0;
		height: 200rpx !important;
	}
	.text19-clz {
		padding-top: 2rpx;
		border-bottom-left-radius: 12rpx;
		padding-left: 10rpx;
		padding-bottom: 10rpx;
		border-top-right-radius: 12rpx;
		margin-right: 10rpx;
		background-color: #e5eae5;
		margin-left: 10rpx;
		flex-shrink: 0;
		overflow: hidden;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 10rpx;
		height: 40rpx !important;
		padding-right: 10rpx;
	}
	.flex16-clz {
		flex-shrink: 0;
		height: 60rpx !important;
	}
	.text20-clz {
		margin-left: 10rpx;
		border: 2rpx solid #aba6a6;
		padding-top: 2rpx;
		padding-left: 10rpx;
		padding-bottom: 4rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
		padding-right: 10rpx;
	}
	.flex2-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.flex6-clz {
		flex: 1;
	}
	.text7-clz {
		margin-left: 10rpx;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text8-clz {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text9-clz {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text10-clz {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text11-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.flex8-clz {
		flex-shrink: 0;
		height: 200rpx !important;
	}
	.text12-clz {
		padding-top: 2rpx;
		border-bottom-left-radius: 12rpx;
		padding-left: 10rpx;
		padding-bottom: 10rpx;
		border-top-right-radius: 12rpx;
		margin-right: 10rpx;
		background-color: #e5eae5;
		margin-left: 10rpx;
		flex-shrink: 0;
		overflow: hidden;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 10rpx;
		height: 40rpx !important;
		padding-right: 10rpx;
	}
	.flex9-clz {
		flex-shrink: 0;
		height: 60rpx !important;
	}
	.text13-clz {
		margin-left: 10rpx;
		border: 2rpx solid #aba6a6;
		padding-top: 2rpx;
		padding-left: 10rpx;
		padding-bottom: 4rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
		padding-right: 10rpx;
	}
	.container328924 {
	}
</style>
