/*  -- flex弹性布局 -- */

.flex {
	display: flex;
}

.basis-xs {
	flex-basis: 20%;
}

.basis-sm {
	flex-basis: 40%;
}

.basis-df {
	flex-basis: 50%;
}

.basis-lg {
	flex-basis: 60%;
}

.basis-xl {
	flex-basis: 80%;
}

.flex1,.flex-sub {
	flex: 1;
}

.flex-twice {
	flex: 2;
}

.flex-treble {
	flex: 3;
}

.flex-direction-column {
	flex-direction: column;
	.flex1{
		flex: inherit;
	}
}

.flex-direction-row-reverse {
	flex-direction: row-reverse;
}

.flex-direction-column-reverse {
	flex-direction: column-reverse;
	.flex1{
		flex: inherit;
	}
}


.flex-wrap {
	flex-wrap: wrap;
}
.flex-nowrap{
	flex-wrap: nowrap;
}
.flex-wrap-reverse {
	flex-wrap:wrap-reverse;
}


.align-start,.items-start {
	align-items: flex-start;
}

.align-end,.items-end {
	align-items: flex-end;
}

.align-center,.items-center {
	align-items: center;
}

.align-stretch,.items-stretch {
	align-items: stretch;
}
.align-baseline,.items-baseline {
	align-items: baseline;
}

.self-start {
	align-self: flex-start;
}

.self-center {
	align-self: flex-center;
}

.self-end {
	align-self: flex-end;
}

.self-stretch {
	align-self: stretch;
}

.align-stretch {
	align-items: stretch;
}

.justify-start {
	justify-content: flex-start;
}

.justify-end {
	justify-content: flex-end;
}

.justify-center {
	justify-content: center;
}

.justify-between {
	justify-content: space-between;
}

.justify-around {
	justify-content: space-around;
}
.items-end{
	align-items: flex-end;
}
.scroll-view{
	overflow-x: auto;
	flex-wrap: nowrap;
	display: flex;
	flex-direction: row !important;
	>.flex{
		overflow-x: auto;
	}
	[class*="diygw-col-"]{
		flex-shrink: 0;
	}
}

.scroll-y{
	flex-wrap: nowrap !important;
	display: flex !important;
	flex-direction: column !important;
}
.flex-content{
	float: none !important;
	flex-wrap: wrap;
}