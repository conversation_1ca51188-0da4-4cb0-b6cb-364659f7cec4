/* ==================
          图片
 ==================== */

 image {
	max-width: 100%;
	display: inline-block;
	position: relative;
	z-index: 0;
}

image.loading::before {
	content: "";
	background-color: #f5f5f5;
	display: block;
	position: absolute;
	width: 100%;
	height: 100%;
	z-index: -2;
}

image.loading::after {
	content: "\e7f1";
	font-family: "diygwIcon";
	position: absolute;
	top: 0;
	left: 0;
	width: 32rpx;
	height: 32rpx;
	line-height: 32rpx;
	right: 0;
	bottom: 0;
	z-index: -1;
	font-size: 32rpx;
	margin: auto;
	color: #ccc;
	-webkit-animation: diygwIcon-spin 2s infinite linear;
	animation: diygwIcon-spin 2s infinite linear;
	display: block;
}