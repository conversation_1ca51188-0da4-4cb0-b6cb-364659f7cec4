
/* -- 阴影 -- */
.diygw-shadow{
	box-shadow:  0 1rpx 6px var(--blackShadow);
}
.diygw-shadow[class*='white'] {
	--ShadowSize: 0 1rpx 6px;
}

.diygw-shadow-lg {
	--ShadowSize: 0rpx 40rpx 100rpx 0rpx;
}

.diygw-shadow-warp {
	position: relative;
	box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
}

.diygw-shadow-warp:before,
.diygw-shadow-warp:after {
	position: absolute;
	content: "";
	top: 20rpx;
	bottom: 30rpx;
	left: 20rpx;
	width: 50%;
	box-shadow: 0 30rpx 20rpx rgba(0, 0, 0, 0.2);
	transform: rotate(-3deg);
	z-index: -1;
}

.diygw-shadow-warp:after {
	right: 20rpx;
	left: auto;
	transform: rotate(3deg);
}

.diygw-shadow-blur {
	position: relative;
}

.diygw-shadow-blur::before {
	content: "";
	display: block;
	background: inherit;
	filter: blur(10rpx);
	position: absolute;
	width: 100%;
	height: 100%;
	top: 10rpx;
	left: 10rpx;
	z-index: -1;
	opacity: 0.3;
	transform-origin: 0 0;
	border-radius: inherit;
	transform: scale(1, 1);
}
