<template>
	<view class="container container328924">
		<view class="flex diygw-col-24 swiper1-clz">
			<swiper :current="swiper1Index" class="swiper" @change="changeSwiper1" indicator-color="rgba(51, 51, 51, 0.39)" indicator-active-color="#fff" indicator-dots="true" autoplay interval="3000" circular="true" style="height: 300rpx">
				<swiper-item class="diygw-swiper-item">
					<view class="diygw-swiper-item-wrap">
						<image src="/static/slider_3.jpg" class="diygw-swiper-image"></image>
					</view>
				</swiper-item>
				<swiper-item class="diygw-swiper-item">
					<view class="diygw-swiper-item-wrap">
						<image src="/static/slider_4.jpg" class="diygw-swiper-image"></image>
					</view>
				</swiper-item>
				<swiper-item class="diygw-swiper-item">
					<view class="diygw-swiper-item-wrap">
						<image src="/static/img_sm_3.jpg" class="diygw-swiper-image"></image>
					</view>
				</swiper-item>
			</swiper>
		</view>
		<view class="flex flex-wrap diygw-col-24 justify-center">
			<view class="flex flex-wrap diygw-col-23 flex-direction-column">
				<view class="flex flex-wrap diygw-col-24">
					<view class="flex flex-wrap diygw-col-0 flex-direction-column justify-center items-center flex4-clz" @tap="navigateTo" data-type="page" data-url="/pages/project/add">
						<text class="diygw-col-0 text-clz diygw-text-xl"> 发布 </text>
					</view>
					<view class="flex flex-wrap diygw-col-0 flex-direction-column justify-center items-center flex11-clz" @tap="navigateTo" data-type="page" data-url="/pages/project/list">
						<text class="diygw-col-0 text1-clz diygw-text-xl"> 搜索 </text>
					</view>
				</view>
			</view>
		</view>
		<view v-for="(item, index) in globalData.list" :key="index" class="flex flex-wrap diygw-col-24 flex-direction-column flex9-clz" @tap="navigateTo" data-type="openmodal">
			<text class="diygw-col-0 text5-clz">
				{{ item.title }}
			</text>
			<text class="diygw-col-0 text6-clz">
				{{ item.type }}
			</text>
			<view class="flex flex-wrap diygw-col-0">
				<text class="diygw-col-0 text7-clz"> 招募人数： {{ item.person_needed }} 人 </text>
			</view>
			<text class="diygw-col-24 text2-clz"> 创建时间： {{ $tools.formatDateTime(item.create_time, 'YYYY-mm-dd HH:MM') }} </text>
			<text class="diygw-col-24 text9-clz"> 结束时间： {{ endingDateReturnFunction({ ending_time: item.ending_time }) }} </text>
		</view>
		<view class="flex diygw-col-24 diygw-bottom">
			<view class="diygw-grid diygw-actions">
				<button @tap="navigateTo" data-type="page" data-url="/pages/home" class="diygw-action">
					<view class="diygw-grid-inner">
						<view class="diygw-grid-icon diygw-avatar" style="">
							<image mode="aspectFit" class="diygw-avatar-img" src="/static/global/grid1.png"></image>
						</view>
						<view class="diygw-grid-title"> 首页 </view>
					</view>
				</button>
				<button @tap="navigateTo" data-type="page" data-url="/pages/project/main_page" class="diygw-action">
					<view class="diygw-grid-inner">
						<view class="diygw-grid-icon diygw-avatar" style="">
							<image mode="aspectFit" class="diygw-avatar-img" src="/static/icon3_dd.png"></image>
						</view>
						<view class="diygw-grid-title"> 项目 </view>
					</view>
				</button>
				<button @tap="navigateTo" data-type="page" data-url="/pages/message/main_page" class="diygw-action">
					<view class="diygw-grid-inner">
						<view class="diygw-grid-icon diygw-avatar" style="">
							<image mode="aspectFit" class="diygw-avatar-img" src="/static/icon3_xx.png"></image>
						</view>
						<view class="diygw-grid-title"> 消息 </view>
					</view>
				</button>
				<button @tap="navigateTo" data-type="page" data-url="/pages/profile/main_page" class="diygw-action">
					<view class="diygw-grid-inner">
						<view class="diygw-grid-icon diygw-avatar" style="">
							<image mode="aspectFit" class="diygw-avatar-img" src="/static/icon1_rzyh.png"></image>
						</view>
						<view class="diygw-grid-title"> 我 </view>
					</view>
				</button>
			</view>
		</view>
		<view class="clearfix"></view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				//用户全局信息
				userInfo: {},
				//页面传参
				globalOption: {},
				//自定义全局变量
				globalData: { list: [{ person_needed: '', create_time: '', _id: '', type: '', title: '' }] },
				swiper1Index: 0
			};
		},
		onShow() {
			this.setCurrentPage(this);
		},
		onLoad(option) {
			this.setCurrentPage(this);
			if (option) {
				this.setData({
					globalOption: this.getOption(option)
				});
			}

			this.init();
		},
		methods: {
			async init() {
				await this.getListFunction();
			},
			// 获取列表 自定义方法
			async getListFunction(param) {
				let thiz = this;
				const res = await uniCloud.importObject('Project').getListForMainPage({ user_id: this.$session.getUserValue('user_id') });

				this.globalData.list = res.data;
			},

			// 返回时间和是否过期 自定义方法
			endingDateReturnFunction(param) {
				let thiz = this;
				let ending_time = param && (param.ending_time || param.ending_time == 0) ? param.ending_time : '';
				var date = this.$tools.formatDateTime(ending_time, 'YYYY-mm-dd HH:MM');
				date += this.$tools.formatDateTime(ending_time, 'YYYY-mm-dd HH:MM') < this.$tools.getCurrentDateTime() ? '(已过期)' : '';
				return date;
			},
			changeSwiper1(evt) {
				let swiper1Index = evt.detail.current;
				this.setData({ swiper1Index });
			}
		}
	};
</script>

<style lang="scss" scoped>
	.swiper1-clz {
		margin-left: 20rpx;
		border-bottom-left-radius: 12rpx;
		overflow: hidden;
		width: calc(100% - 20rpx - 20rpx) !important;
		border-top-left-radius: 12rpx;
		margin-top: 6rpx;
		border-top-right-radius: 12rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 20rpx;
		margin-right: 20rpx;
	}
	.flex4-clz {
		margin-left: 0rpx;
		border: 2rpx solid #aba6a6;
		flex-shrink: 0;
		width: 348rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		height: 200rpx !important;
		margin-right: 4rpx;
	}
	.text-clz {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.flex11-clz {
		margin-left: 6rpx;
		border: 2rpx solid #aba6a6;
		flex-shrink: 0;
		width: 348rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		height: 200rpx !important;
		margin-right: 0rpx;
	}
	.text1-clz {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.flex9-clz {
		margin-left: 0rpx;
		flex: 1;
		width: calc(100% - 0rpx - 0rpx) !important;
		margin-top: 10rpx;
		margin-bottom: 20rpx;
		border-bottom: 2rpx solid #eee;
		margin-right: 0rpx;
	}
	.text5-clz {
		margin-left: 10rpx;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text6-clz {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text7-clz {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 80rpx;
	}
	.text2-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text9-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.container328924 {
		padding-bottom: 80px;
	}
</style>
