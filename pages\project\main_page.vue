<template>
	<view class="container container332681 safe-bottom">
		<!-- 搜索栏 -->
		<view class="flex flex-wrap diygw-col-24">
			<view class="flex diygw-col-24">
				<view class="diygw-search">
					<view class="flex1 align-center flex padding-xs solid radius">
						<text style="color: #555 !important" class="diy-icon-search"></text>
						<input class="flex1" name="search" type="text" v-model="search" placeholder="搜索项目" />
					</view>
					<view style="color: #333 !important"
						  class="diygw-tag margin-left-xs radius-xs"
						  @tap="handleSearch">
						搜索
					</view>
				</view>
			</view>
			<!-- 当前筛选类型提示 -->
			<view v-if="currentFilter" class="filter-indicator">
				<text>当前筛选: {{ currentFilter }}</text>
				<text class="clear-filter" @tap="clearFilter">清除筛选</text>
			</view>
		</view>

		<view class="flex flex-wrap diygw-col-24 flex-direction-column">
			<view class="flex diygw-col-24">
				<view class="diygw-grid col-5">
					<view class="diygw-grid-item" @tap="filterByType('项目协作')" :class="{'active-filter': currentFilter === '项目协作'}">
						<view class="diygw-grid-inner">
							<view class="diygw-grid-icon diygw-avatar grid-icon-clz">
								<image mode="aspectFit" class="diygw-avatar-img" src="/static/cxcp.png"></image>
							</view>
							<view class="diygw-grid-title"> 项目协作 </view>
						</view>
					</view>
					<view class="diygw-grid-item" @tap="filterByType('竞赛组队')" :class="{'active-filter': currentFilter === '竞赛组队'}">
						<view class="diygw-grid-inner">
							<view class="diygw-grid-icon diygw-avatar grid-icon-clz">
								<image mode="aspectFit" class="diygw-avatar-img" src="/static/cy3.png"></image>
							</view>
							<view class="diygw-grid-title"> 竞赛组队 </view>
						</view>
					</view>
					<view class="diygw-grid-item" @tap="filterByType('科研招募')" :class="{'active-filter': currentFilter === '科研招募'}">
						<view class="diygw-grid-inner">
							<view class="diygw-grid-icon diygw-avatar grid-icon-clz">
								<image mode="aspectFit" class="diygw-avatar-img" src="/static/dc.png"></image>
							</view>
							<view class="diygw-grid-title"> 科研招募 </view>
						</view>
					</view>
					<view class="diygw-grid-item" @tap="filterByType('大创计划')" :class="{'active-filter': currentFilter === '大创计划'}">
						<view class="diygw-grid-inner">
							<view class="diygw-grid-icon diygw-avatar grid-icon-clz">
								<image mode="aspectFit" class="diygw-avatar-img" src="/static/zh.png"></image>
							</view>
							<view class="diygw-grid-title"> 大创计划 </view>
						</view>
					</view>
					<view class="diygw-grid-item" @tap="navigateToUserList">
						<view class="diygw-grid-inner">
							<view class="diygw-grid-icon diygw-avatar grid-icon-clz">
								<image mode="aspectFit" class="diygw-avatar-img" src="/static/project_action/user_list.svg"></image>
							</view>
							<view class="diygw-grid-title"> 用户列表 </view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 项目列表 -->
		<view v-for="(item, index) in globalData.list" :key="index"
			class="flex flex-wrap diygw-col-24 flex-direction-column flex7-clz"
			@tap="navigateToProjectDetail(item)">
			<view class="flex flex-wrap diygw-col-24 items-stretch">
				<view class="flex flex-wrap diygw-col-0 flex-direction-column justify-between flex6-clz">
					<view class="flex flex-wrap diygw-col-0 items-center">
						<text class="diygw-col-0 text3-clz">{{ item.title }}</text>
					</view>
					<view class="flex flex-wrap diygw-col-0 items-center flex10-clz">
						<text class="diygw-col-0 text4-clz"></text>
						<image :src="getCreatorAvatar(item)" class="image1-size diygw-image diygw-col-0 image1-clz" mode="widthFix"></image>
						<text class="diygw-col-0 text6-clz">{{ item.type === 'teacher' ? '导师发起' : '学生发起' }}</text>
						<text class="diygw-col-0 text6-clz">{{ item.project_cat ? item.project_cat.name : (item.type || '科技创新') }}</text>
					</view>
				</view>
				<image :src="getProjectImage(item)" class="image2-size diygw-image diygw-col-0 image2-clz" mode="widthFix"></image>
			</view>
			<view class="flex flex-wrap diygw-col-24 justify-between items-center flex12-clz">
				<view class="flex flex-wrap diygw-col-0 items-center description-container">
					<text class="diygw-col-0">简介：</text>
					<text class="diygw-col-0 description-text">{{ getProjectDescription(item) }}</text>
				</view>
				<view class="flex flex-wrap diygw-col-0 items-center">
					<text class="flex icon diygw-col-0 diy-icon-attention"></text>
					<text class="diygw-col-0">{{ item.view_count || 0 }}</text>
				</view>
			</view>
			<view class="flex flex-wrap diygw-col-24 justify-between items-center flex15-clz">
				<view class="flex flex-wrap diygw-col-0 items-center">
					<text class="diygw-col-0">截止：{{ endingDateReturnFunction({ ending_time: item.ending_time }) }}</text>
				</view>
				<view class="flex flex-wrap diygw-col-0 items-center">
					<text class="diygw-col-0">需求{{ item.current_members || 0 }}/{{ item.person_needed || 0 }}人 · {{ item.current_person_request || 0 }}人申请</text>
				</view>
			</view>
		</view>

		<!-- 项目详情弹窗 -->
		<view @touchmove.stop.prevent="" v-if="modal" class="diygw-modal bottom-modal" :class="modal" style="z-index: 1000000">
			<view class="diygw-dialog diygw-dialog-modal">
				<view class="justify-end diygw-bar">
					<view class="content"> 项目描述 </view>
					<view class="action" data-type="closemodal" data-id="modal" @tap="navigateTo">
						<text class="diy-icon-close"></text>
					</view>
				</view>
				<view>
					<view class="flex diygw-dialog-content">
						<text class="diygw-col-24 text1-clz diygw-text-md">
							{{ globalData.showProjectDetail.title }}
						</text>
						<text v-if="globalData.showProjectDetail.comp_name" class="diygw-col-24 diygw-text-sm"> 竞赛：{{ globalData.showProjectDetail.comp_name }} </text>
						<text class="diygw-col-24 diygw-text-sm"> 招募人数： {{ globalData.showProjectDetail.person_needed }} </text>
						<text class="diygw-col-24 diygw-text-sm" v-if="globalData.showProjectDetail.detail"> 已加入： {{ globalData.showProjectDetail.detail.person_pending }} </text>
						<text class="diygw-col-24 diygw-text-sm"> 项目创建时间: {{ $tools.formatDateTime(globalData.showProjectDetail.create_time, 'YYYY-mm-dd HH:MM') }} </text>
						<text class="diygw-col-24 diygw-text-sm"> 项目结束时间: {{ $tools.formatDateTime(globalData.showProjectDetail.ending_time, 'YYYY-mm-dd HH:MM') }} </text>
						<!-- 优先显示content_text，保留换行 -->
						<view v-if="globalData.showProjectDetail.detail && globalData.showProjectDetail.detail.content_text" class="diygw-col-24 content-text preserve-whitespace">
							{{ globalData.showProjectDetail.detail.content_text }}
						</view>
						<!-- 其次显示HTML格式的description -->
						<mp-html v-else-if="globalData.showProjectDetail.detail && globalData.showProjectDetail.detail.description" :content="globalData.showProjectDetail.detail.description" class="diygw-col-24 ucontent1-clz"></mp-html>
						<button v-show="!globalData.showProjectDetail.in_project && globalData.showProjectDetail.ending_time > secondFunction()" @tap="openRequestModalFunction" class="diygw-col-24 btn-clz diygw-btn-default">申请加入</button>
						<button v-show="!(globalData.showProjectDetail.ending_time > secondFunction())" class="diygw-col-24 btn1-clz diygw-btn-default">已过期</button>
					</view>
				</view>
			</view>
		</view>

		<!-- 申请加入弹窗 -->
		<view @touchmove.stop.prevent="" v-if="modal1" class="diygw-modal basic" :class="modal1" style="z-index: 1000000">
			<view class="diygw-dialog diygw-dialog-modal1 basis-lg">
				<view class="justify-end diygw-bar">
					<view class="content"> 申请加入 </view>
					<view class="action" data-type="closemodal" data-id="modal1" @tap="navigateTo">
						<text class="diy-icon-close"></text>
					</view>
				</view>
				<view>
					<view class="flex diygw-dialog-content">
						<text class="diygw-col-24"> 主题：{{ globalData.showProjectDetail.title }} </text>
						<u-form-item class="diygw-col-24" labelPosition="top" prop="self_introduce">
							<u-input maxlength="200" height="60px" class="" placeholder="自我介绍（请介绍自己以提升被选中的可能性）" v-model="self_introduce" type="textarea"></u-input>
						</u-form-item>
					</view>
				</view>
				<view class="flex justify-end">
					<button @tap="requestToJoinFunction" class="diygw-btn green flex1 margin-xs">申请</button>
				</view>
			</view>
		</view>

		<view class="flex diygw-col-0 right-bottom floatbar-clz">
			<view class="diygw-grid diygw-actions">
				<button class="diygw-action" @tap="navigateTo" data-type="page" data-url="/pages/project/add">
					<view class="diygw-grid-inner">
						<view class="diygw-grid-icon diygw-avatar diy-icon-roundaddfill" style="color: #07c160"> </view>
						<view class="diygw-grid-title"></view>
					</view>
				</button>
			</view>
		</view>
	</view>
		<!-- 加载更多区域 -->
		<view v-if="globalData.list.length > 0" class="load-more-area">
			<view v-if="isLoading && currentPage > 1" class="loading-indicator">
				<text>加载中...</text>
			</view>
			<view v-else-if="hasMore" class="load-more-btn" @tap="loadMore">
				<text>加载更多</text>
			</view>
			<view v-else class="no-more-data">
				<text>没有更多项目了</text>
			</view>
		</view>

		<!-- 底部占位元素，确保有足够的滚动空间 -->
		<view class="bottom-spacer"></view>
</template>

<script>
	import {
		mapState,
		mapActions
	} from 'pinia';
	import {
		useProjectStore
	} from '@/store/project.js';

	export default {
		data() {
			return {
				// 本地组件状态
			};
		},
		computed: {
			...mapState(useProjectStore, [
				'globalData',
				'search',
				'currentFilter',
				'modal',
				'modal1',
				'self_introduce',
				'isLoading',
				'hasMore',
				'currentPage'
			])
		},
		onShow() {
			this.setCurrentPage(this);
			if (this.isInitialized && uni.getStorageSync('project_list_need_refresh')) {
				console.log('检测到需要刷新项目列表');
				uni.removeStorageSync('project_list_need_refresh');
				this.getListFunction(false);
			}
			// 更新消息tabbar徽标
			this.updateMessageBadge();
		},
		onLoad(option) {
			this.setCurrentPage(this);
			if (option) {
				this.setData({
					globalOption: this.getOption(option)
				});
			}
			this.init();
		},
		onUnload() {
			this.isInitialized = false;
			this.isLoading = false;
			console.log('页面卸载，重置初始化状态');
		},
		onPageScroll(e) {
			const windowHeight = uni.getSystemInfoSync().windowHeight;
			if (this.currentPage > 1 && e.scrollTop + windowHeight + 150 >= e.scrollHeight && !this.isLoading && this.hasMore) {
				this.loadMore();
			}
		},
		methods: {
			...mapActions(useProjectStore, [
				'init',
				'getListFunction',
				'handleSearch',
				'filterByType',
				'clearFilter',
				'showProjectDetailFunction',
				'openRequestModalFunction',
				'requestToJoinFunction',
				'loadMore',
				'navigateToProjectDetail',
				'navigateToUserList',
				'endingDateReturnFunction',
				'getProjectDescription',
				'getCreatorAvatar',
				'getProjectImage'
			]),
			// 更新消息tabbar徽标
			updateMessageBadge() {
				try {
					// 检查用户是否已登录
					const userInfo = uni.getStorageSync('userInfo');
					if (userInfo && userInfo.user_id) {
						// 调用全局方法更新徽标
						getApp().updateMessageTabBarBadge();
					}
				} catch (error) {
					console.error('更新消息徽标失败:', error);
				}
			},

			// 兼容性方法
			setCurrentPage(page) {
				// Vue3 不需要设置 $scope，直接用 this 即可
			},
			getOption(option) {
				return option;
			},
			setData(data) {
				Object.assign(this, data);
			}
		}
	};
</script>

<style lang="scss" scoped>
	.flex9-clz {
		margin-left: 0rpx;
		flex: 1;
		width: calc(100%) !important;
		margin-top: 10rpx;
		margin-bottom: 20rpx;
		border-bottom: 2rpx solid #eee;
		margin-right: 0rpx;
	}
	/* 网格项样式 */
	.grid-icon-clz {
		font-size: 36px !important;
	}
	.floatbar-clz {
		transform: translate(0rpx, 0rpx) scale(2, 2);
		bottom: 280rpx; /* 进一步增加底部距离，避免被 tabbar 遮挡 */
		width: 80rpx !important;
		right: 60rpx;
		z-index: 900;
	}
	.flex7-clz {
		border: 2rpx solid #eee;
		padding-top: 20rpx;
		border-bottom-left-radius: 24rpx;
		padding-left: 20rpx;
		padding-bottom: 20rpx;
		border-top-right-radius: 24rpx;
		margin-right: 20rpx;
		margin-left: 20rpx;
		box-shadow: 0rpx 2rpx 6rpx rgba(31, 31, 31, 0.16);
		overflow: hidden;
		width: calc(100% - 20rpx - 20rpx) !important;
		border-top-left-radius: 24rpx;
		margin-top: 10rpx;
		border-bottom-right-radius: 24rpx;
		margin-bottom: 30rpx; /* 增加底部边距 */
		padding-right: 20rpx;
	}

	/* 添加一个类来为最后一个项目增加额外的底部边距 */
	.flex7-clz:last-child {
		margin-bottom: 100rpx; /* 为最后一个项目增加更大的底部边距 */
	}
	.flex6-clz {
		flex: 1;
	}
	.text3-clz {
		margin-left: 10rpx;
		font-weight: bold;
		font-size: 28rpx !important;
		margin-top: 0rpx;
		margin-bottom: 0rpx;
		margin-right: 0rpx;
	}
	.flex10-clz {
		margin-left: 0rpx;
		margin-top: 10rpx;
		margin-bottom: 0rpx;
		margin-right: 0rpx;
	}
	.text4-clz {
		margin-left: 0rpx;
		color: #e2585d;
		margin-top: 0rpx;
		margin-bottom: 0rpx;
		margin-right: 10rpx;
	}
	.image1-clz {
		border-bottom-left-radius: 40rpx;
		overflow: hidden;
		border-top-left-radius: 40rpx;
		border-top-right-radius: 40rpx;
		border-bottom-right-radius: 40rpx;
	}
	.image1-size {
		height: 40rpx !important;
		width: 40rpx !important;
	}
	.text6-clz {
		padding-top: 6rpx;
		border-bottom-left-radius: 32rpx;
		color: #ffffff;
		padding-left: 12rpx;
		padding-bottom: 6rpx;
		border-top-right-radius: 32rpx;
		margin-right: 10rpx;
		background-color: #fe9834;
		margin-left: 10rpx;
		overflow: hidden;
		border-top-left-radius: 32rpx;
		margin-top: 10rpx;
		border-bottom-right-radius: 32rpx;
		margin-bottom: 10rpx;
		padding-right: 12rpx;
	}
	.image2-clz {
		border-bottom-left-radius: 12rpx;
		overflow: hidden;
		border-top-left-radius: 12rpx;
		border-top-right-radius: 12rpx;
		border-bottom-right-radius: 12rpx;
	}
	.image2-size {
		height: 120rpx !important;
		width: 120rpx !important;
	}
	.flex12-clz {
		padding-top: 10rpx;
		padding-left: 0rpx;
		padding-bottom: 10rpx;
		border-bottom: 2rpx solid #eee;
		padding-right: 0rpx;
	}
	.text8-clz {
		padding-top: 6rpx;
		border-bottom-left-radius: 12rpx;
		padding-left: 12rpx;
		padding-bottom: 6rpx;
		border-top-right-radius: 12rpx;
		margin-right: 10rpx;
		background-color: #edecec;
		margin-left: 10rpx;
		overflow: hidden;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 10rpx;
		padding-right: 12rpx;
	}
	.text-clz {
		padding-top: 6rpx;
		border-bottom-left-radius: 12rpx;
		padding-left: 12rpx;
		padding-bottom: 6rpx;
		border-top-right-radius: 12rpx;
		margin-right: 10rpx;
		background-color: #edecec;
		margin-left: 10rpx;
		overflow: hidden;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 10rpx;
		padding-right: 12rpx;
	}
	.text9-clz {
		padding-top: 6rpx;
		border-bottom-left-radius: 12rpx;
		padding-left: 12rpx;
		padding-bottom: 6rpx;
		border-top-right-radius: 12rpx;
		margin-right: 10rpx;
		background-color: #edecec;
		margin-left: 10rpx;
		overflow: hidden;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 10rpx;
		padding-right: 12rpx;
	}
	.icon {
		font-size: 40rpx;
	}
	.flex15-clz {
		padding-top: 20rpx;
		padding-left: 0rpx;
		padding-bottom: 20rpx;
		border-bottom: 2rpx solid #eee;
		padding-right: 0rpx;
	}
	.container332681 {
		padding-bottom: 180rpx; /* 进一步增加底部内边距，为 tabbar 预留更多空间 */
		margin-bottom: 0;
		min-height: 100vh; /* 确保容器至少有一个屏幕高度 */
	}

	/* 安全区域底部样式 */
	.safe-bottom {
		padding-bottom: calc(180rpx + constant(safe-area-inset-bottom)); /* iOS 11.0 之前 */
		padding-bottom: calc(180rpx + env(safe-area-inset-bottom)); /* iOS 11.0 之后 */
	}

	/* 底部占位元素样式 */
	.bottom-spacer {
		height: 200rpx; /* 足够的高度确保内容不被 tabbar 遮挡 */
		width: 100%;
		clear: both;
	}

	/* 加载更多区域样式 */
	.load-more-area {
		padding: 30rpx 0;
		text-align: center;
		width: 100%;
	}

	/* 保留空白符和换行 */
	.preserve-whitespace {
		white-space: pre-wrap; /* 保留空白符和换行符，并正常换行 */
		word-wrap: break-word; /* 确保长单词也能换行 */
	}

	.loading-indicator {
		color: #999;
		font-size: 28rpx;
	}

	.load-more-btn {
		background-color: #f8f8f8;
		color: #07c160;
		font-size: 28rpx;
		padding: 15rpx 0;
		border-radius: 10rpx;
		margin: 0 30rpx;
	}

	.no-more-data {
		color: #999;
		font-size: 28rpx;
	}
	.flex-direction-column {
		flex-direction: column;
	}
	.items-end {
		align-items: flex-end;
	}
	.active-filter {
		background-color: #f0f0f0;
	}
	.filter-indicator {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
		padding: 10rpx;
		border: 1rpx solid #eee;
		border-radius: 12rpx;
		background-color: #f0f0f0;
	}
	.clear-filter {
		margin-left: 10rpx;
		color: #07c160;
		font-size: 28rpx;
		font-weight: bold;
	}
	.creator-name {
		background-color: #e6f7ff !important;
		color: #0066cc !important;
		border: 1px solid #91d5ff !important;
	}
	.show-types-container {
		margin-top: 10rpx;
		margin-left: 10rpx;
		padding: 10rpx;
		border: 1rpx solid #eee;
		border-radius: 12rpx;
		background-color: #f0f0f0;
	}
	.show-types-btn {
		color: #07c160;
		font-size: 28rpx;
		font-weight: bold;
	}

	/* 弹窗样式 */
	.diygw-modal {
		position: fixed;
		top: 0;
		right: 0;
		bottom: 0;
		left: 0;
		z-index: 1110;
		opacity: 0;
		outline: 0;
		text-align: center;
		-ms-transform: scale(1.185);
		transform: scale(1.185);
		backface-visibility: hidden;
		perspective: 2000rpx;
		background: rgba(0, 0, 0, 0.6);
		transition: all 0.3s ease-in-out 0s;
		pointer-events: none;
	}

	.diygw-modal.show {
		opacity: 1;
		transition-duration: 0.3s;
		-ms-transform: scale(1);
		transform: scale(1);
		overflow-x: hidden;
		overflow-y: auto;
		pointer-events: auto;
	}

	.diygw-dialog {
		position: relative;
		margin: 0 auto;
		max-width: 100%;
		background-color: #ffffff;
		border-radius: 10rpx;
		overflow: hidden;
	}

	.diygw-modal.bottom-modal .diygw-dialog {
		width: 100%;
		border-radius: 0;
		position: absolute;
		bottom: 0;
	}

	.diygw-dialog-content {
		padding: 30rpx;
		max-height: 60vh;
		overflow-y: auto;
	}

	.diygw-bar {
		min-height: 100rpx;
		box-sizing: border-box;
		overflow: hidden;
		display: flex;
		align-items: center;
		justify-content: space-between;
		padding: 0 30rpx;
		border-bottom: 1rpx solid #eee;
	}

	.btn-clz {
		background-color: #07c160;
		color: #ffffff;
		border-radius: 50rpx;
		font-size: 32rpx;
		height: 90rpx;
		line-height: 90rpx;
		margin: 20rpx auto 0;
		width: 100%;
		text-align: center;
	}

	.btn1-clz {
		background-color: #cccccc;
		color: #ffffff;
		border-radius: 50rpx;
		font-size: 32rpx;
		height: 90rpx;
		line-height: 90rpx;
		margin: 20rpx auto 0;
		width: 100%;
		text-align: center;
	}

	.text1-clz {
		font-size: 30rpx !important;
		font-weight: bold;
		margin-bottom: 10rpx;
	}

	.ucontent1-clz {
		flex-shrink: 0;
		width: 100% !important;
		font-size: 26rpx !important;
		margin-top: 20rpx;
	}

	.description-container {
		flex: 1;
		overflow: hidden;
	}

	.description-text {
		overflow: hidden;
		text-overflow: ellipsis;
		white-space: nowrap;
		max-width: 400rpx;
		color: #666;
	}
</style>
