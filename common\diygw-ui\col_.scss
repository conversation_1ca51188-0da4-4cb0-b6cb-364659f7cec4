

@use 'sass:math';

[class*=diygw-col-] {
    float: left;
    box-sizing: border-box;
}
.diygw-col-0{
  width: auto;
}



@for $i from 1 through 24 {
    .diygw-col-#{$i} {
      width: (math.div(1, 24) * $i * 100) * 1% !important;
    }
  
    .diygw-col-offset-#{$i} {
      margin-left: (math.div(1, 24) * $i * 100) * 1%;
    }
  
    .diygw-col-pull-#{$i} {
      position: relative;
      right: (math.div(1, 24) * $i * 100) * 1%;
    }
  
    .diygw-col-push-#{$i} {
      position: relative;
      left: (math.div(1, 24) * $i * 100) * 1%;
    }
}


