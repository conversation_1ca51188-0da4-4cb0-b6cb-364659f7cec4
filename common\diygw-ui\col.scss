
[class*=diygw-col-] {
  float: left;
  box-sizing: border-box;
  position: relative;
}
.diygw-col-0 {
  width: auto;
}
.diygw-col-1 {
  width: 4.1666666667% !important;
}
.diygw-col-offset-1 {
  margin-left: 4.1666666667%;
}
.diygw-col-pull-1 {
  position: relative;
  right: 4.1666666667%;
}
.diygw-col-push-1 {
  position: relative;
  left: 4.1666666667%;
}
.diygw-col-2 {
  width: 8.3333333333% !important;
}
.diygw-col-offset-2 {
  margin-left: 8.3333333333%;
}
.diygw-col-pull-2 {
  position: relative;
  right: 8.3333333333%;
}
.diygw-col-push-2 {
  position: relative;
  left: 8.3333333333%;
}
.diygw-col-3 {
  width: 12.5% !important;
}
.diygw-col-offset-3 {
  margin-left: 12.5%;
}
.diygw-col-pull-3 {
  position: relative;
  right: 12.5%;
}
.diygw-col-push-3 {
  position: relative;
  left: 12.5%;
}
.diygw-col-4 {
  width: 16.6666666667% !important;
}
.diygw-col-offset-4 {
  margin-left: 16.6666666667%;
}
.diygw-col-pull-4 {
  position: relative;
  right: 16.6666666667%;
}
.diygw-col-push-4 {
  position: relative;
  left: 16.6666666667%;
}
.diygw-col-5 {
  width: 20.8333333333% !important;
}
.diygw-col-offset-5 {
  margin-left: 20.8333333333%;
}
.diygw-col-pull-5 {
  position: relative;
  right: 20.8333333333%;
}
.diygw-col-push-5 {
  position: relative;
  left: 20.8333333333%;
}
.diygw-col-6 {
  width: 25% !important;
}
.diygw-col-offset-6 {
  margin-left: 25%;
}
.diygw-col-pull-6 {
  position: relative;
  right: 25%;
}
.diygw-col-push-6 {
  position: relative;
  left: 25%;
}
.diygw-col-7 {
  width: 29.1666666667% !important;
}
.diygw-col-offset-7 {
  margin-left: 29.1666666667%;
}
.diygw-col-pull-7 {
  position: relative;
  right: 29.1666666667%;
}
.diygw-col-push-7 {
  position: relative;
  left: 29.1666666667%;
}
.diygw-col-8 {
  width: 33.3333333333% !important;
}
.diygw-col-offset-8 {
  margin-left: 33.3333333333%;
}
.diygw-col-pull-8 {
  position: relative;
  right: 33.3333333333%;
}
.diygw-col-push-8 {
  position: relative;
  left: 33.3333333333%;
}
.diygw-col-9 {
  width: 37.5% !important;
}
.diygw-col-offset-9 {
  margin-left: 37.5%;
}
.diygw-col-pull-9 {
  position: relative;
  right: 37.5%;
}
.diygw-col-push-9 {
  position: relative;
  left: 37.5%;
}
.diygw-col-10 {
  width: 41.6666666667% !important;
}
.diygw-col-offset-10 {
  margin-left: 41.6666666667%;
}
.diygw-col-pull-10 {
  position: relative;
  right: 41.6666666667%;
}
.diygw-col-push-10 {
  position: relative;
  left: 41.6666666667%;
}
.diygw-col-11 {
  width: 45.8333333333% !important;
}
.diygw-col-offset-11 {
  margin-left: 45.8333333333%;
}
.diygw-col-pull-11 {
  position: relative;
  right: 45.8333333333%;
}
.diygw-col-push-11 {
  position: relative;
  left: 45.8333333333%;
}
.diygw-col-12 {
  width: 50% !important;
}
.diygw-col-offset-12 {
  margin-left: 50%;
}
.diygw-col-pull-12 {
  position: relative;
  right: 50%;
}
.diygw-col-push-12 {
  position: relative;
  left: 50%;
}
.diygw-col-13 {
  width: 54.1666666667% !important;
}
.diygw-col-offset-13 {
  margin-left: 54.1666666667%;
}
.diygw-col-pull-13 {
  position: relative;
  right: 54.1666666667%;
}
.diygw-col-push-13 {
  position: relative;
  left: 54.1666666667%;
}
.diygw-col-14 {
  width: 58.3333333333% !important;
}
.diygw-col-offset-14 {
  margin-left: 58.3333333333%;
}
.diygw-col-pull-14 {
  position: relative;
  right: 58.3333333333%;
}
.diygw-col-push-14 {
  position: relative;
  left: 58.3333333333%;
}
.diygw-col-15 {
  width: 62.5% !important;
}
.diygw-col-offset-15 {
  margin-left: 62.5%;
}
.diygw-col-pull-15 {
  position: relative;
  right: 62.5%;
}
.diygw-col-push-15 {
  position: relative;
  left: 62.5%;
}
.diygw-col-16 {
  width: 66.6666666667% !important;
}
.diygw-col-offset-16 {
  margin-left: 66.6666666667%;
}
.diygw-col-pull-16 {
  position: relative;
  right: 66.6666666667%;
}
.diygw-col-push-16 {
  position: relative;
  left: 66.6666666667%;
}
.diygw-col-17 {
  width: 70.8333333333% !important;
}
.diygw-col-offset-17 {
  margin-left: 70.8333333333%;
}
.diygw-col-pull-17 {
  position: relative;
  right: 70.8333333333%;
}
.diygw-col-push-17 {
  position: relative;
  left: 70.8333333333%;
}
.diygw-col-18 {
  width: 75% !important;
}
.diygw-col-offset-18 {
  margin-left: 75%;
}
.diygw-col-pull-18 {
  position: relative;
  right: 75%;
}
.diygw-col-push-18 {
  position: relative;
  left: 75%;
}
.diygw-col-19 {
  width: 79.1666666667% !important;
}
.diygw-col-offset-19 {
  margin-left: 79.1666666667%;
}
.diygw-col-pull-19 {
  position: relative;
  right: 79.1666666667%;
}
.diygw-col-push-19 {
  position: relative;
  left: 79.1666666667%;
}
.diygw-col-20 {
  width: 83.3333333333% !important;
}
.diygw-col-offset-20 {
  margin-left: 83.3333333333%;
}
.diygw-col-pull-20 {
  position: relative;
  right: 83.3333333333%;
}
.diygw-col-push-20 {
  position: relative;
  left: 83.3333333333%;
}
.diygw-col-21 {
  width: 87.5% !important;
}
.diygw-col-offset-21 {
  margin-left: 87.5%;
}
.diygw-col-pull-21 {
  position: relative;
  right: 87.5%;
}
.diygw-col-push-21 {
  position: relative;
  left: 87.5%;
}
.diygw-col-22 {
  width: 91.6666666667% !important;
}
.diygw-col-offset-22 {
  margin-left: 91.6666666667%;
}
.diygw-col-pull-22 {
  position: relative;
  right: 91.6666666667%;
}
.diygw-col-push-22 {
  position: relative;
  left: 91.6666666667%;
}
.diygw-col-23 {
  width: 95.8333333333% !important;
}
.diygw-col-offset-23 {
  margin-left: 95.8333333333%;
}
.diygw-col-pull-23 {
  position: relative;
  right: 95.8333333333%;
}
.diygw-col-push-23 {
  position: relative;
  left: 95.8333333333%;
}
.diygw-col-24 {
  width: 100% !important;
}
.diygw-col-offset-24 {
  margin-left: 100%;
}
.diygw-col-pull-24 {
  position: relative;
  right: 100%;
}
.diygw-col-push-24 {
  position: relative;
  left: 100%;
}