{"bsonType": "object", "required": ["title", "avatar", "paragraph", "operated_by"], "permission": {"read": true, "create": true, "update": true, "delete": true}, "properties": {"_id": {"description": "新闻ID，系统自动生成"}, "title": {"bsonType": "string", "title": "主题", "description": "新闻主题", "maxLength": 255, "trim": "both", "order": 1}, "paragraph": {"bsonType": "string", "title": "文章", "description": "新闻内容"}, "avatar": {"bsonType": "file", "title": "封面大图", "description": "缩略图地址", "label": "封面大图", "order": 2}, "status": {"bsonType": "int", "title": "文章状态", "description": "文章状态：0 草稿箱 1 已发布", "defaultValue": 0, "enum": [{"value": 0, "text": "草稿箱"}, {"value": 1, "text": "已发布"}]}, "operated_by": {"bsonType": "string", "title": "操作员ID", "description": "操作员ID", "foreignKey": "uni-id-users._id", "enum": {"collection": "uni-id-users", "field": "_id as value, nickname as text"}}, "view_count": {"bsonType": "int", "title": "阅读数量", "description": "阅读数量", "permission": {"write": false}}, "create_time": {"bsonType": "timestamp", "title": "创建时间", "description": "创建时间", "forceDefaultValue": {"$env": "now"}}, "update_time": {"bsonType": "timestamp", "title": "更新时间", "description": "更新时间", "forceDefaultValue": {"$env": "now"}}, "remove_time": {"bsonType": "timestamp", "title": "移除时间", "description": "移除时间"}}}