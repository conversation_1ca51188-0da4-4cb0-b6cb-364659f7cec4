<template>
	<view class="container container328924">
		<view class="flex flex-wrap diygw-col-24 flex-direction-column">
			<view class="flex diygw-col-24">
				<view class="diygw-search">
					<view class="flex1 align-center flex padding-xs solid radius">
						<text style="color: #555 !important" class="diy-icon-search"></text>
						<input class="flex1" name="search" type="" v-model="search" placeholder="请输入关键字" />
					</view>
					<view style="color: #333 !important" class="diygw-tag margin-left-xs radius-xs"> 搜索 </view>
				</view>
			</view>
			<view class="flex tabs diygw-col-24 flex-direction-column">
				<scroll-view scroll-x :show-scrollbar="false" enhanced :scroll-into-view="'scroll-' + tabsIndex" :scroll-left="tabsLeft" scroll-with-animation class="diygw-tabs text-center solid-bottom justify-center scale-title small-border tabs-title">
					<view :id="'scroll-' + index" class="diygw-tab-item tabs-item-title flex-sub" :class="index == tabsIndex ? '    cur text-green  ' : ''" v-for="(item, index) in tabsDatas" :key="index" @click="changeTabs" :data-index="index"> <text v-if="item.icon" :class="item.icon"></text> {{ item.text }} </view>
				</scroll-view>
				<view class="">
					<view v-if="tabsIndex == 0" class="flex-sub"> </view>
				</view>
			</view>
		</view>
		<view v-for="(tabItem, tabIndex) in tabsDatas" :key="tabIndex" class="flex diygw-col-24 flex-direction-column justify-center items-center flex-nowrap">
			<view v-if="tabsIndex == tabIndex" v-for="(item, index) in globalData.projectList[this.tabsNow].data" :key="index" class="flex diygw-col-24 flex-direction-column flex-nowrap flex18-clz" @tap="navigateTo" data-type="showProjectDetailFunction" :data-id="item._id" :data-index="index">
				<text @tap="navigateTo" data-type="showProjectDetailFunction" class="diygw-col-0 text5-clz">
					{{ item.title }}
				</text>
				<text v-if="item.comp_name" @tap="navigateTo" data-type="showProjectDetailFunction" class="diygw-col-0 text9-clz"> 竞赛：{{ item.comp_name }} </text>
				<view class="flex flex-wrap diygw-col-0">
					<text class="diygw-col-0 text18-clz"> 招募人数： {{ item.person_needed ?? '' }} 人 </text>
				</view>
				<view class="flex diygw-col-0 flex-nowrap flex-clz">
					<view class="flex flex-wrap diygw-col-24 flex-direction-column">
						<text class="diygw-col-0 text23-clz"> 创建时间： {{ $tools.formatDateTime(item.create_time, 'YYYY-mm-dd HH:MM') }} </text>
						<text class="diygw-col-0 text-clz"> 结束时间： {{ endingDateReturnFunction({ ending_time: item.ending_time }) }} </text>
					</view>
					<view v-if="item.in_project" class="flex flex-wrap diygw-col-5 flex-direction-column">
						<text class="diygw-col-0 text11-clz"> 已加入 </text>
					</view>
				</view>
			</view>
		</view>
		<view @touchmove.stop.prevent="" v-if="modal1" class="diygw-modal basic" :class="modal1" style="z-index: 1000000">
			<view class="diygw-dialog diygw-dialog-modal1 basis-lg">
				<view class="justify-end diygw-bar">
					<view class="content"> 申请加入 </view>
					<view class="action" data-type="closemodal" data-id="modal1" @tap="navigateTo">
						<text class="diy-icon-close"></text>
					</view>
				</view>
				<view>
					<view class="flex diygw-dialog-content">
						<text class="diygw-col-24"> 主题：{{ globalData.showProjectDetail.title }} </text>
						<u-form-item class="diygw-col-24" labelPosition="top" prop="self_introduce">
							<u-input maxlength="200" height="60px" class="" placeholder="自我介绍（请介绍自己以提升被选中的可能性）" v-model="self_introduce" type="textarea"></u-input>
						</u-form-item>
					</view>
				</view>
				<view class="flex justify-end">
					<button @tap="navigateTo" data-type="requestToJoinFunction" class="diygw-btn green flex1 margin-xs">申请</button>
				</view>
			</view>
		</view>
		<view @touchmove.stop.prevent="" v-if="modal" class="diygw-modal bottom-modal" :class="modal" style="z-index: 1000000">
			<view class="diygw-dialog diygw-dialog-modal">
				<view class="justify-end diygw-bar">
					<view class="content"> 项目描述 </view>
					<view class="action" data-type="closemodal" data-id="modal" @tap="navigateTo">
						<text class="diy-icon-close"></text>
					</view>
				</view>
				<view>
					<view class="flex diygw-dialog-content">
						<text class="diygw-col-24 text1-clz diygw-text-md">
							{{ globalData.showProjectDetail.title }}
						</text>
						<text v-if="globalData.showProjectDetail.comp_name" class="diygw-col-24 diygw-text-sm"> 竞赛：{{ globalData.showProjectDetail.comp_name }} </text>
						<text class="diygw-col-24 diygw-text-sm"> 招募人数： {{ globalData.showProjectDetail.person_needed }} </text>
						<text class="diygw-col-24 diygw-text-sm"> 已加入： {{ globalData.showProjectDetail.detail.person_pending }} </text>
						<text class="diygw-col-24 diygw-text-sm"> 项目创建时间: {{ $tools.formatDateTime(globalData.showProjectDetail.create_time, 'YYYY-mm-dd HH:MM') }} </text>
						<text class="diygw-col-24 diygw-text-sm"> 项目结束时间: {{ $tools.formatDateTime(globalData.showProjectDetail.ending_time, 'YYYY-mm-dd HH:MM') }} </text>
						<mp-html :content="globalData.showProjectDetail.detail.description" class="diygw-col-24 ucontent1-clz"></mp-html>
						<button v-show="!globalData.showProjectDetail.in_project && globalData.showProjectDetail.ending_time > secondFunction()" @tap="navigateTo" data-type="openRequestModalFunction" class="diygw-col-24 btn-clz diygw-btn-default">申请加入</button>
						<button v-show="!(globalData.showProjectDetail.ending_time > secondFunction())" class="diygw-col-24 btn1-clz diygw-btn-default">已过期</button>
					</view>
				</view>
			</view>
		</view>
		<view class="flex diygw-col-24 diygw-bottom">
			<view class="diygw-grid diygw-actions">
				<button @tap="navigateTo" data-type="page" data-url="/pages/home" class="diygw-action">
					<view class="diygw-grid-inner">
						<view class="diygw-grid-icon diygw-avatar" style="">
							<image mode="aspectFit" class="diygw-avatar-img" src="/static/global/grid1.png"></image>
						</view>
						<view class="diygw-grid-title"> 首页 </view>
					</view>
				</button>
				<button @tap="navigateTo" data-type="page" data-url="/pages/project/main_page" class="diygw-action">
					<view class="diygw-grid-inner">
						<view class="diygw-grid-icon diygw-avatar" style="">
							<image mode="aspectFit" class="diygw-avatar-img" src="/static/icon3_dd.png"></image>
						</view>
						<view class="diygw-grid-title"> 项目 </view>
					</view>
				</button>
				<button @tap="navigateTo" data-type="page" data-url="/pages/message/main_page" class="diygw-action">
					<view class="diygw-grid-inner">
						<view class="diygw-grid-icon diygw-avatar" style="">
							<image mode="aspectFit" class="diygw-avatar-img" src="/static/icon3_xx.png"></image>
						</view>
						<view class="diygw-grid-title"> 消息 </view>
					</view>
				</button>
				<button @tap="navigateTo" data-type="page" data-url="/pages/profile/main_page" class="diygw-action">
					<view class="diygw-grid-inner">
						<view class="diygw-grid-icon diygw-avatar" style="">
							<image mode="aspectFit" class="diygw-avatar-img" src="/static/icon1_rzyh.png"></image>
						</view>
						<view class="diygw-grid-title"> 我 </view>
					</view>
				</button>
			</view>
		</view>
		<view class="clearfix"></view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				//用户全局信息
				userInfo: {},
				//页面传参
				globalOption: {},
				//自定义全局变量
				globalData: { projectList: { '': { data: [{ person_needed: 0, ending_time: 1732924800, create_time: 1732105910821, user_id: '', _id: '', title: '' }], load: 1 } }, showProjectDetail: {} },
				search: '',
				tabsDatas: [{ text: ``, icon: `` }],
				tabsTimer: null,
				tabsMainIndex: 0,
				tabsLeft: 0,
				tabsWidth: 0,
				tabsItemWidth: 0,
				tabsIndex: 0,
				modal1: '',
				self_introduce: '',
				modal: ''
			};
		},
		computed: {
			tabsNow: function () {
				return this.tabsDatas[this.tabsIndex].text;
			}
		},
		onShow() {
			this.setCurrentPage(this);
		},
		onLoad(option) {
			this.setCurrentPage(this);
			if (option) {
				this.setData({
					globalOption: this.getOption(option)
				});
			}

			this.init();
		},
		methods: {
			async init() {
				await this.getProjectCatFunction();
			},
			// 获取项目类型和默认项目列 自定义方法
			async getProjectCatFunction(param) {
				let thiz = this;
				const res = await uniCloud.importObject('Initialize').getProjectCategory();

				this.tabsDatas = [];

				for (const i in res.data) {
					this.tabsDatas.push({
						text: res.data[i].name,
						icon: ''
					});
					this.globalData.projectList[res.data[i].name] = {
						data: [],
						load: 0
					};
				}

				await this.getProjectListFunction({});
			},

			// tabBar被点击 自定义方法
			async tabBarEventFunction(param) {
				let thiz = this;
				await this.getProjectListFunction({});
			},

			// 获取项目列 自定义方法
			async getProjectListFunction(param) {
				let thiz = this;
				let catDatas = param && (param.catDatas || param.catDatas == 0) ? param.catDatas : thiz.tabsDatas || '';
				const catName = catDatas[this.tabsIndex].text;
				if (this.globalData.projectList[catName].load) return;

				const res = await uniCloud.importObject('Project').getListByCat({ name: catName, user_id: this.$session.getUserValue('user_id') });

				this.globalData.projectList[catName].data = res.data;
				this.globalData.projectList[catName].load = 1;
			},

			// 展现项目详情 自定义方法
			async showProjectDetailFunction(param) {
				let thiz = this;
				const index = param.index;

				this.globalData.showProjectDetail = this.globalData.projectList[this.tabsNow].data[index];

				if (!this.globalData.showProjectDetail.hasOwnProperty('detail')) {
					const res = await uniCloud.importObject('Project').getDetailFromList({
						id: param.id
					});
					if (res.status == 0) {
						uni.showToast({
							title: res.msg,
							icon: 'error',
							duration: 2000
						});

						return;
					}

					this.globalData.showProjectDetail['detail'] = res.data;
				}

				this.navigateTo({
					type: 'openmodal',
					id: 'modal'
				});
			},

			// 打开申请加入模块 自定义方法
			async openRequestModalFunction(param) {
				let thiz = this;
				if (!this.$session.getToken()) {
					//比如未登录，转身到其他页面等
					this.showToast('请先登录');

					this.navigateTo({
						type: 'page',
						url: 'sign/login'
					});

					return;
				}

				this.navigateTo({
					type: 'openmodal',
					id: 'modal1'
				});
				this.navigateTo({
					type: 'closemodal',
					id: 'modal'
				});
			},

			// 申请加入项目 自定义方法
			async requestToJoinFunction(param) {
				let thiz = this;
				const res = await uniCloud.importObject('ProjectAction').requestJoin({
					project_id: this.globalData.showProjectDetail._id,
					introduce: this.self_introduce,
					user_id: this.$session.getUserValue('user_id')
				});

				uni.showToast({
					icon: res.status ? 'success' : 'error',
					title: res.msg
				});

				if (res.status == 1) {
					this.navigateTo({
						type: 'closemodal',
						id: 'modal1'
					});
				}
			},

			// 秒timestamp 自定义方法
			secondFunction(param) {
				let thiz = this;
				const timestampMs = Date.now();
				const timestampSeconds = Math.floor(timestampMs / 1000);
				return timestampSeconds;
			},

			// 返回时间和是否过期 自定义方法
			endingDateReturnFunction(param) {
				let thiz = this;
				let ending_time = param && (param.ending_time || param.ending_time == 0) ? param.ending_time : '';
				var date = this.$tools.formatDateTime(ending_time, 'YYYY-mm-dd HH:MM');
				date += this.$tools.formatDateTime(ending_time, 'YYYY-mm-dd HH:MM') < this.$tools.getCurrentDateTime() ? '(已过期)' : '';
				return date;
			},
			// 获取一个目标元素的高度
			getElTabsRect(elClass, dataVal) {
				new Promise((resolve, reject) => {
					const query = uni.createSelectorQuery().in(this);
					query
						.select('.' + elClass)
						.fields(
							{
								size: true
							},
							(res) => {
								// 如果节点尚未生成，res值为null，循环调用执行
								if (!res) {
									setTimeout(() => {
										this.getElTabsRect(elClass);
									}, 10);
									return;
								}
								this[dataVal] = res.width;
								resolve();
							}
						)
						.exec();
				});
			},
			// 设置左边菜单的滚动状态
			async setTabsStatus(index) {
				this.current = index;
				// 如果为0，意味着尚未初始化
				if (this.tabsWidth == 0 || this.tabsItemWidth == 0) {
					await this.getElTabsRect('tabs-title', 'tabsWidth');
					await this.getElTabsRect('tabs-item-title', 'tabsItemWidth');
				}
				// 将菜单活动item垂直居中
				this.tabsLeft = index * this.tabsItemWidth + this.tabsItemWidth / 2 - this.tabsWidth / 2;
			},
			changeTabs(evt) {
				let { index } = evt.currentTarget.dataset;
				if (index == this.tabsIndex) return;
				this.setData({
					tabsIndex: index
				});
				this.setTabsStatus(index);
				this.navigateTo({ type: 'tabBarEventFunction' });
			}
		}
	};
</script>

<style lang="scss" scoped>
	.tabs .diygw-tab-item {
		height: 54rpx;
		line-height: 54rpx;
	}
	.flex18-clz {
		flex: 1;
		border-bottom: 2rpx solid #eee;
	}
	.text5-clz {
		margin-left: 10rpx;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text9-clz {
		margin-left: 10rpx;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text18-clz {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 80rpx;
	}
	.flex-clz {
		flex: 1;
	}
	.text23-clz {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text-clz {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text11-clz {
		padding-top: 2rpx;
		border-bottom-left-radius: 12rpx;
		bottom: 0rpx;
		padding-left: 10rpx;
		padding-bottom: 10rpx;
		border-top-right-radius: 12rpx;
		margin-right: 10rpx;
		background-color: #e5eae5;
		margin-left: 10rpx;
		flex-shrink: 0;
		overflow: hidden;
		left: 0rpx;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-bottom-right-radius: 12rpx;
		position: absolute;
		margin-bottom: 10rpx;
		height: 40rpx !important;
		padding-right: 10rpx;
	}
	.modal1-clz {
		z-index: 1000000;
	}
	.diygw-dialog-modal1 {
	}
	.modal-clz {
		z-index: 1000000;
	}
	.diygw-dialog-modal {
	}
	.text1-clz {
		font-size: 30rpx !important;
	}
	.ucontent1-clz {
		flex-shrink: 0;
		width: 100% !important;
		font-size: 26rpx !important;
	}
	.btn-clz {
		background-color: #07c160;
		padding-top: 20rpx;
		color: #fff;
		padding-left: 20rpx;
		padding-bottom: 20rpx;
		text-align: center;
		padding-right: 20rpx;
	}
	.btn1-clz {
		background-color: #b9bfbb;
		padding-top: 20rpx;
		color: #050505;
		padding-left: 20rpx;
		padding-bottom: 20rpx;
		text-align: center;
		padding-right: 20rpx;
	}
	.container328924 {
		padding-bottom: 80px;
	}
</style>
