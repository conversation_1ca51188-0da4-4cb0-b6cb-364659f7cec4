<template>
	<view class="container container328924">
		<view class="flex flex-wrap diygw-col-24 flex-direction-column flex-clz">
			<rich-text :nodes="text" class="diygw-col-24 text-clz diygw-text-lg"></rich-text>
			<text class="diygw-col-24 text1-clz"> 项目类型 </text>
			<text class="diygw-col-24 text2-clz"> 项目具体情况（你已被允许加入项目，是否接受） </text>
			<view class="flex diygw-col-24 button-clz">
				<button class="diygw-btn red radius-xs flex-sub margin-xs button-button-clz">退出</button>
				<button class="diygw-btn green radius-xs flex-sub margin-xs button-button-clz">加入</button>
			</view>
		</view>
		<view class="flex flex-wrap diygw-col-24 flex-direction-column flex1-clz">
			<rich-text :nodes="text3" class="diygw-col-24 text3-clz diygw-text-lg"></rich-text>
			<text class="diygw-col-24 text4-clz"> 项目类型 </text>
			<text class="diygw-col-24 text5-clz"> 项目具体情况（你已被允许加入项目，是否接受） </text>
			<view class="flex diygw-col-24 button1-clz">
				<button class="diygw-btn red radius-xs flex-sub margin-xs button1-button-clz">退出</button>
				<button class="diygw-btn green radius-xs flex-sub margin-xs button1-button-clz">加入</button>
			</view>
		</view>
		<view class="flex flex-wrap diygw-col-24 flex-direction-column flex3-clz">
			<rich-text :nodes="text9" class="diygw-col-24 text9-clz diygw-text-lg"></rich-text>
			<text class="diygw-col-24 text10-clz"> 项目类型 </text>
			<text class="diygw-col-24 text11-clz"> 项目具体情况（你已被允许加入项目，是否接受） </text>
			<view class="flex diygw-col-24 button3-clz">
				<button class="diygw-btn red radius-xs flex-sub margin-xs button3-button-clz">退出</button>
				<button class="diygw-btn green radius-xs flex-sub margin-xs button3-button-clz">加入</button>
			</view>
		</view>
		<view class="flex flex-wrap diygw-col-24 flex-direction-column flex2-clz">
			<rich-text :nodes="text6" class="diygw-col-24 text6-clz diygw-text-lg"></rich-text>
			<text class="diygw-col-24 text7-clz"> 项目类型 </text>
			<text class="diygw-col-24 text8-clz"> 项目具体情况（你已被允许加入项目，是否接受） </text>
			<view class="flex diygw-col-24 button2-clz">
				<button class="diygw-btn red radius-xs flex-sub margin-xs button2-button-clz">退出</button>
				<button class="diygw-btn green radius-xs flex-sub margin-xs button2-button-clz">加入</button>
			</view>
		</view>
		<view class="clearfix"></view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				//用户全局信息
				userInfo: {},
				//页面传参
				globalOption: {},
				//自定义全局变量
				globalData: {},
				text: `<p><span style="font-size: 18px;">项目名</span></p>`,
				text3: `<p><span style="font-size: 18px;">项目名</span></p>`,
				text9: `<p><span style="font-size: 18px;">项目名</span></p>`,
				text6: `<p><span style="font-size: 18px;">项目名</span></p>`
			};
		},
		onShow() {
			this.setCurrentPage(this);
		},
		onLoad(option) {
			this.setCurrentPage(this);
			if (option) {
				this.setData({
					globalOption: this.getOption(option)
				});
			}

			this.init();
		},
		methods: {
			async init() {}
		}
	};
</script>

<style lang="scss" scoped>
	.flex-clz {
		margin-left: 10rpx;
		border-bottom-left-radius: 12rpx;
		box-shadow: 6rpx 6rpx 6rpx rgba(31, 31, 31, 0.16);
		overflow: hidden;
		width: calc(100% - 10rpx - 10rpx) !important;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-top-right-radius: 12rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text-clz {
		margin-left: 10rpx;
		flex-shrink: 0;
		width: calc(100% - 10rpx - 10rpx) !important;
		margin-top: 10rpx;
		margin-bottom: 4rpx;
		border-bottom: 2rpx solid #aba6a6;
		height: 68rpx !important;
		margin-right: 10rpx;
	}
	.text1-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		font-size: 28rpx !important;
		margin-top: 10rpx;
		margin-bottom: 4rpx;
		margin-right: 10rpx;
	}
	.text2-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		font-size: 28rpx !important;
		margin-top: 0rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.button-clz {
		border-bottom-left-radius: 12rpx;
		box-shadow: 4rpx 8rpx 6rpx rgba(31, 31, 31, 0.16);
		overflow: hidden;
		border-top-left-radius: 12rpx;
		border-top-right-radius: 12rpx;
		border-bottom-right-radius: 12rpx;
	}
	.button-button-clz {
		margin: 6rpx !important;
	}
	.flex1-clz {
		margin-left: 10rpx;
		border-bottom-left-radius: 12rpx;
		box-shadow: 4rpx 8rpx 6rpx rgba(31, 31, 31, 0.16);
		overflow: hidden;
		width: calc(100% - 10rpx - 10rpx) !important;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-top-right-radius: 12rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text3-clz {
		margin-left: 10rpx;
		flex-shrink: 0;
		width: calc(100% - 10rpx - 10rpx) !important;
		margin-top: 10rpx;
		margin-bottom: 4rpx;
		border-bottom: 2rpx solid #aba6a6;
		height: 68rpx !important;
		margin-right: 10rpx;
	}
	.text4-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		font-size: 28rpx !important;
		margin-top: 10rpx;
		margin-bottom: 4rpx;
		margin-right: 10rpx;
	}
	.text5-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		font-size: 28rpx !important;
		margin-top: 0rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.button1-clz {
		border-bottom-left-radius: 12rpx;
		box-shadow: 4rpx 8rpx 6rpx rgba(31, 31, 31, 0.16);
		overflow: hidden;
		border-top-left-radius: 12rpx;
		border-top-right-radius: 12rpx;
		border-bottom-right-radius: 12rpx;
	}
	.button1-button-clz {
		margin: 6rpx !important;
	}
	.flex3-clz {
		margin-left: 10rpx;
		border-bottom-left-radius: 12rpx;
		box-shadow: 4rpx 8rpx 6rpx rgba(31, 31, 31, 0.16);
		overflow: hidden;
		width: calc(100% - 10rpx - 10rpx) !important;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-top-right-radius: 12rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text9-clz {
		margin-left: 10rpx;
		flex-shrink: 0;
		width: calc(100% - 10rpx - 10rpx) !important;
		margin-top: 10rpx;
		margin-bottom: 4rpx;
		border-bottom: 2rpx solid #aba6a6;
		height: 68rpx !important;
		margin-right: 10rpx;
	}
	.text10-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		font-size: 28rpx !important;
		margin-top: 10rpx;
		margin-bottom: 4rpx;
		margin-right: 10rpx;
	}
	.text11-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		font-size: 28rpx !important;
		margin-top: 0rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.button3-clz {
		border-bottom-left-radius: 12rpx;
		box-shadow: 4rpx 8rpx 6rpx rgba(31, 31, 31, 0.16);
		overflow: hidden;
		border-top-left-radius: 12rpx;
		border-top-right-radius: 12rpx;
		border-bottom-right-radius: 12rpx;
	}
	.button3-button-clz {
		margin: 6rpx !important;
	}
	.flex2-clz {
		margin-left: 10rpx;
		border-bottom-left-radius: 12rpx;
		box-shadow: 4rpx 8rpx 6rpx rgba(31, 31, 31, 0.16);
		overflow: hidden;
		width: calc(100% - 10rpx - 10rpx) !important;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-top-right-radius: 12rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text6-clz {
		margin-left: 10rpx;
		flex-shrink: 0;
		width: calc(100% - 10rpx - 10rpx) !important;
		margin-top: 10rpx;
		margin-bottom: 4rpx;
		border-bottom: 2rpx solid #aba6a6;
		height: 68rpx !important;
		margin-right: 10rpx;
	}
	.text7-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		font-size: 28rpx !important;
		margin-top: 10rpx;
		margin-bottom: 4rpx;
		margin-right: 10rpx;
	}
	.text8-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		font-size: 28rpx !important;
		margin-top: 0rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.button2-clz {
		border-bottom-left-radius: 12rpx;
		box-shadow: 4rpx 8rpx 6rpx rgba(31, 31, 31, 0.16);
		overflow: hidden;
		border-top-left-radius: 12rpx;
		border-top-right-radius: 12rpx;
		border-bottom-right-radius: 12rpx;
	}
	.button2-button-clz {
		margin: 6rpx !important;
	}
	.container328924 {
	}
</style>
