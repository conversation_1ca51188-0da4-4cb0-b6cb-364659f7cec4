{"bsonType": "object", "required": ["college_id", "name"], "permission": {"read": true, "create": true, "update": true, "delete": true}, "properties": {"_id": {"description": "ID，系统自动生成", "title": "科系id"}, "college_id": {"bsonType": "string", "title": "学院", "description": "关联的学院ID", "foreignKey": "xm-stp-college_cat._id", "enum": {"collection": "xm-stp-college_cat", "field": "_id as value, name as text"}}, "name": {"bsonType": "string", "title": "科系", "description": "科系名称", "default": "0", "minLength": 1, "maxLength": 100}}}