<template>
	<view class="container container328924">
		<text class="diygw-col-24 diygw-text-md"> 内容类型 </text>
		<view class="flex flex-wrap diygw-col-24 flex-direction-column flex16-clz">
			<view class="flex flex-wrap diygw-col-24 items-stretch flex12-clz">
				<view class="flex flex-wrap diygw-col-0 flex-direction-column items-start">
					<image src="/static/10.jpg" class="image8-size diygw-image diygw-col-0 image8-clz" mode="widthFix"></image>
				</view>
				<view class="flex flex-wrap diygw-col-0 flex-direction-column justify-center flex24-clz">
					<view class="flex flex-wrap diygw-col-24 justify-between items-center flex53-clz">
						<text class="diygw-col-0"> 邓志锋 </text>
						<text class="diygw-col-0"> 21:24 </text>
					</view>
					<text class="diygw-col-0 text24-clz"> DIY可视化功能非常强大，我很喜欢 </text>
				</view>
			</view>
			<view class="flex flex-wrap diygw-col-24 items-stretch flex11-clz">
				<view class="flex flex-wrap diygw-col-0 flex-direction-column items-start">
					<image src="/static/1.png" class="image-size diygw-image diygw-col-0 image-clz" mode="widthFix"></image>
				</view>
				<view class="flex flex-wrap diygw-col-0 flex-direction-column justify-center flex20-clz">
					<view class="flex flex-wrap diygw-col-24 justify-between items-center flex22-clz">
						<text class="diygw-col-0"> 邓志锋 </text>
						<text class="diygw-col-0"> 一天前 </text>
					</view>
					<text class="diygw-col-0 text20-clz"> DIY可视化真心不错，丰富的教程，丰富的教程，丰富的教程，丰富的教程，丰富的教程，丰富的教程，丰富的教程 </text>
				</view>
			</view>
			<view class="flex flex-wrap diygw-col-24 items-stretch flex23-clz">
				<view class="flex flex-wrap diygw-col-0 flex-direction-column items-start">
					<image src="/static/1.png" class="image1-size diygw-image diygw-col-0 image1-clz" mode="widthFix"></image>
				</view>
				<view class="flex flex-wrap diygw-col-0 flex-direction-column justify-center flex26-clz">
					<view class="flex flex-wrap diygw-col-24 justify-between items-center flex27-clz">
						<text class="diygw-col-0"> 邓志锋 </text>
						<text class="diygw-col-0"> 一天前 </text>
					</view>
					<text class="diygw-col-0 text23-clz"> DIY可视化真心不错，丰富的教程，丰富的教程，丰富的教程，丰富的教程，丰富的教程，丰富的教程，丰富的教程 </text>
				</view>
			</view>
		</view>
		<view class="clearfix"></view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				//用户全局信息
				userInfo: {},
				//页面传参
				globalOption: {},
				//自定义全局变量
				globalData: {}
			};
		},
		onShow() {
			this.setCurrentPage(this);
		},
		onLoad(option) {
			this.setCurrentPage(this);
			if (option) {
				this.setData({
					globalOption: this.getOption(option)
				});
			}

			this.init();
		},
		methods: {
			async init() {}
		}
	};
</script>

<style lang="scss" scoped>
	.flex16-clz {
		padding-top: 10rpx;
		border-bottom-left-radius: 12rpx;
		padding-left: 10rpx;
		padding-bottom: 10rpx;
		border-top-right-radius: 12rpx;
		margin-right: 10rpx;
		background-color: #ffffff;
		margin-left: 10rpx;
		overflow: hidden;
		width: calc(100% - 10rpx - 10rpx) !important;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 10rpx;
		padding-right: 10rpx;
	}
	.flex12-clz {
		margin-left: 0rpx;
		width: calc(100% - 0rpx - 0rpx) !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 0rpx;
	}
	.image8-clz {
		border-bottom-left-radius: 100rpx;
		overflow: hidden;
		border-top-left-radius: 100rpx;
		border-top-right-radius: 100rpx;
		border-bottom-right-radius: 100rpx;
	}
	.image8-size {
		height: 96rpx !important;
		width: 96rpx !important;
	}
	.flex24-clz {
		flex: 1;
		border-bottom: 2rpx solid #eee;
	}
	.flex53-clz {
		padding-top: 10rpx;
		color: #9d9d9d;
		flex: 1;
		padding-left: 10rpx;
		padding-bottom: 10rpx;
		padding-right: 10rpx;
	}
	.text24-clz {
		padding-top: 10rpx;
		flex: 1;
		padding-left: 10rpx;
		padding-bottom: 10rpx;
		padding-right: 10rpx;
	}
	.flex11-clz {
		margin-left: 0rpx;
		width: calc(100% - 0rpx - 0rpx) !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 0rpx;
	}
	.image-clz {
		border-bottom-left-radius: 100rpx;
		overflow: hidden;
		border-top-left-radius: 100rpx;
		border-top-right-radius: 100rpx;
		border-bottom-right-radius: 100rpx;
	}
	.image-size {
		height: 96rpx !important;
		width: 96rpx !important;
	}
	.flex20-clz {
		flex: 1;
		border-bottom: 2rpx solid #eee;
	}
	.flex22-clz {
		padding-top: 10rpx;
		color: #9d9d9d;
		flex: 1;
		padding-left: 10rpx;
		padding-bottom: 10rpx;
		padding-right: 10rpx;
	}
	.text20-clz {
		padding-top: 10rpx;
		flex: 1;
		padding-left: 10rpx;
		padding-bottom: 10rpx;
		padding-right: 10rpx;
	}
	.flex23-clz {
		margin-left: 0rpx;
		width: calc(100% - 0rpx - 0rpx) !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 0rpx;
	}
	.image1-clz {
		border-bottom-left-radius: 100rpx;
		overflow: hidden;
		border-top-left-radius: 100rpx;
		border-top-right-radius: 100rpx;
		border-bottom-right-radius: 100rpx;
	}
	.image1-size {
		height: 96rpx !important;
		width: 96rpx !important;
	}
	.flex26-clz {
		flex: 1;
		border-bottom: 2rpx solid #eee;
	}
	.flex27-clz {
		padding-top: 10rpx;
		color: #9d9d9d;
		flex: 1;
		padding-left: 10rpx;
		padding-bottom: 10rpx;
		padding-right: 10rpx;
	}
	.text23-clz {
		padding-top: 10rpx;
		flex: 1;
		padding-left: 10rpx;
		padding-bottom: 10rpx;
		padding-right: 10rpx;
	}
	.container328924 {
	}
</style>
