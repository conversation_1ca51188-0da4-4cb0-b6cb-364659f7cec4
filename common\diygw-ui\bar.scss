/* ==================
          操作条
 ==================== */

 .diygw-bottom{
	position: fixed !important;
	left:0;
	bottom:0;
	width:100%;
	background: #fff;
	z-index:999999;
	padding-bottom: 0;
	padding-bottom: constant(safe-area-inset-bottom);
	padding-bottom: env(safe-area-inset-bottom);
}
 
.diygw-bar {
	display: flex;
	position: relative;
	align-items: center;
	min-height: 100rpx;
	justify-content: space-between;
}

.diygw-bar .action {
	display: flex;
	align-items: center;
	height: 100%;
	justify-content: center;
	max-width: 100%;
}

.diygw-bar .action.border-title {
	position: relative;
	top: -10rpx;
}

.diygw-bar .action.border-title text[class*="bg-"]:last-child {
	position: absolute;
	bottom: -0.5rem;
	min-width: 2rem;
	height: 6rpx;
	left: 0;
}

.diygw-bar .action.sub-title {
	position: relative;
	top: -0.2rem;
}

.diygw-bar .action.sub-title text {
	position: relative;
	z-index: 1;
}

.diygw-bar .action.sub-title text[class*="bg-"]:last-child {
	position: absolute;
	display: inline-block;
	bottom: -0.2rem;
	border-radius: 6rpx;
	width: 100%;
	height: 0.6rem;
	left: 0.6rem;
	opacity: 0.3;
	z-index: 0;
}

.diygw-bar .action.sub-title text[class*="text-"]:last-child {
	position: absolute;
	display: inline-block;
	bottom: -0.7rem;
	left: 0.5rem;
	opacity: 0.2;
	z-index: 0;
	text-align: right;
	font-weight: 900;
	font-size: 36rpx;
}

.diygw-bar.justify-center .action.border-title text:last-child,
.diygw-bar.justify-center .action.sub-title text:last-child {
	left: 0;
	right: 0;
	margin: auto;
	text-align: center;
}

.diygw-bar .action:first-child {
	margin-left: 30rpx;
	font-size: 30rpx;
}

.diygw-bar .action text.text-cut {
	text-align: left;
	width: 100%;
}

.diygw-bar .diygw-avatar:first-child {
	margin-left: 20rpx;
}

.diygw-bar .action:first-child>text[class*="diygwIcon-"] {
	margin-left: -0.3em;
	margin-right: 0.3em;
}

.diygw-bar .action:last-child {
	margin-right: 30rpx;
}

.diygw-bar .action>text[class*="diygwIcon-"],
.diygw-bar .action>view[class*="diygwIcon-"] {
	font-size:36rpx;
}

.diygw-bar .action>text[class*="diygwIcon-"]+text[class*="diygwIcon-"] {
	margin-left: 0.5em;
}

.diygw-bar .content {
	position: absolute;
	text-align: center;
	width: calc(100% - 340rpx);
	left: 0;
	right: 0;
	bottom: 0;
	top: 0;
	margin: auto;
	height: 60rpx;
	font-size: 32rpx;
	line-height: 60rpx;
	cursor: none;
	pointer-events: none;
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
}

.diygw-bar.ios .content {
	bottom: 14rpx;
	height: 60rpx;
	font-size: 32rpx;
	line-height: 60rpx;
}

.diygw-bar.btn-group {
	justify-content: space-around;
}

.diygw-bar.btn-group button {
	padding: 20rpx 32rpx;
}

.diygw-bar.btn-group button {
	flex: 1;
	margin: 0 20rpx;
	max-width: 50%;
}

.diygw-bar .search-form {
	background-color: #f5f5f5;
	line-height: 64rpx;
	height: 64rpx;
	font-size: 24rpx;
	color: var(--black);
	flex: 1;
	display: flex;
	align-items: center;
	margin: 0 30rpx;
}

.diygw-bar .search-form+.action {
	margin-right: 30rpx;
}

.diygw-bar .search-form input {
	flex: 1;
	padding-right: 30rpx;
	height: 64rpx;
	line-height: 64rpx;
	font-size: 16rpx;
	background-color: transparent;
}

.diygw-bar .search-form [class*="diygwIcon-"] {
	margin: 0 0.5em 0 0.8em;
}

.diygw-bar .search-form [class*="diygwIcon-"]::before {
	top: 0px;
}

.diygw-bar.fixed,.nav.fixed {
	position: fixed;
	width: 100%;
	top: 0;
	z-index: 1024;
	box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.1);
}

.diygw-bar.foot {
	position: fixed;
	width: 100%;
	bottom: 0;
	z-index: 1024;
	box-shadow: 0 -1rpx 6rpx rgba(0, 0, 0, 0.1);
}

.diygw-bar.tabbar {
	padding: 0;
	height: calc(100rpx + env(safe-area-inset-bottom) / 2);
	padding-bottom: calc(env(safe-area-inset-bottom) / 2);
}

.diygw-tabbar-height {
	min-height: 100rpx;
	height: calc(100rpx + env(safe-area-inset-bottom) / 2);
}

.diygw-bar.tabbar.shadow {
	box-shadow: 0 -1rpx 6rpx rgba(0, 0, 0, 0.1);
}

.diygw-bar.tabbar .action {
	font-size: 24rpx;
	position: relative;
	flex: 1;
	text-align: center;
	padding: 0;
	display: block;
	height: auto;
	line-height: 1;
	margin: 0;
	overflow: initial;
}

.diygw-bar.tabbar.shop .action {
	width: 280px;
	flex: initial;
}

.diygw-bar.tabbar .action.add-action {
	position: relative;
	z-index: 2;
	padding-top: 50rpx;
	background-color: inherit;
}

.diygw-bar.tabbar .action.add-action [class*="diygwIcon-"] {
	position: absolute;
	width: 70rpx;
	z-index: 2;
	height: 70rpx;
	border-radius: 50%;
	line-height: 70rpx;
	font-size: 50rpx;
	top: -35rpx;
	left: 0;
	right: 0;
	margin: auto;
	padding: 0;
}

.diygw-bar.tabbar .action.add-action::after {
	content: "";
	position: absolute;
	width: 100rpx;
	height: 100rpx;
	top: -50rpx;
	left: 0;
	right: 0;
	margin: auto;
	box-shadow: 0 -3rpx 8rpx rgba(0, 0, 0, 0.08);
	border-radius: 50rpx;
	background-color: inherit;
	z-index: 0;
}

.diygw-bar.tabbar .action.add-action::before {
	content: "";
	position: absolute;
	width: 100rpx;
	height: 30rpx;
	bottom: 30rpx;
	left: 0;
	right: 0;
	margin: auto;
	background-color: inherit;
	z-index: 1;
}

.diygw-bar.tabbar .btn-group {
	flex: 1;
	display: flex;
	justify-content: space-around;
	align-items: center;
	padding: 0 10rpx;
}

.diygw-bar.tabbar button.action::after {
	border: 0;
}

.diygw-bar.tabbar .action [class*="diygwIcon-"] {
	width: 100rpx;
	position: relative;
	display: block;
	height: auto;
	margin: 0 auto 10rpx;
	text-align: center;
	font-size: 40px;
}

.diygw-bar.tabbar .action .diygwIcon-diygw-image {
	margin: 0 auto;
}

.diygw-bar.tabbar .action .diygwIcon-diygw-image image {
	width: 50rpx;
	height: 50rpx;
	display: inline-block;
}

.diygw-bar.tabbar .submit {
	align-items: center;
	display: flex;
	justify-content: center;
	text-align: center;
	position: relative;
	flex: 2;
	align-self: stretch;
}

.diygw-bar.tabbar .submit:last-child {
	flex: 2.6;
}

.diygw-bar.tabbar .submit+.submit {
	flex: 2;
}

.diygw-bar.tabbar.border .action::before {
	content: " ";
	width: 200%;
	height: 200%;
	position: absolute;
	top: 0;
	left: 0;
	transform: scale(0.5);
	transform-origin: 0 0;
	border-right: 1rpx solid rgba(0, 0, 0, 0.1);
	z-index: 3;
}

.diygw-bar.tabbar.border .action:last-child:before {
	display: none;
}

.diygw-bar.input {
	padding-right: 20rpx;
	background-color: var(--white);
}

.diygw-bar.input input {
	overflow: initial;
	line-height: 64rpx;
	height: 64rpx;
	min-height: 64rpx;
	flex: 1;
	font-size: 30rpx;
	margin: 0 20rpx;
}

.diygw-bar.input .action {
	margin-left: 20rpx;
}

.diygw-bar.input .action [class*="diygwIcon-"] {
	font-size: 48rpx;
}

.diygw-bar.input input+.action {
	margin-right: 20rpx;
	margin-left: 0px;
}

.diygw-bar.input .action:first-child [class*="diygwIcon-"] {
	margin-left: 0px;
}
