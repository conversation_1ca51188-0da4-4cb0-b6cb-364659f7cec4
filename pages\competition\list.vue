<template>
	<view class="container container328924">
		<view v-for="(item, index) in globalData.competitionList" :key="index" class="flex flex-wrap diygw-col-24 flex-direction-column justify-between flex47-clz" @tap="navigateTo" data-type="page" data-url="/pages/competition/detail" :data-id="item._id">
			<text class="diygw-text-line4 diygw-col-24 text43-clz">
				{{ item.title }}
			</text>
			<view class="flex flex-wrap diygw-col-0 justify-between flex48-clz">
				<text class="diygw-col-24 text42-clz">
					{{ $tools.formatDateTime(item.create_time, 'YYYY-mm-dd HH:MM') }}
				</text>
			</view>
		</view>
		<view class="clearfix"></view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				//用户全局信息
				userInfo: {},
				//页面传参
				globalOption: {},
				//自定义全局变量
				globalData: { competitionList: [] }
			};
		},
		onShow() {
			this.setCurrentPage(this);
		},
		onLoad(option) {
			this.setCurrentPage(this);
			if (option) {
				this.setData({
					globalOption: this.getOption(option)
				});
			}

			this.init();
		},
		methods: {
			async init() {
				await this.getListFunction();
			},
			// 获取竞赛列表 自定义方法
			async getListFunction(param) {
				let thiz = this;
				const res = await uniCloud.importObject('Competition').getList(8);

				if (res.status == 0) {
					uni.showToast({
						title: res.msg,
						icon: 'error',
						duration: 2000
					});

					return;
				}

				this.globalData.competitionList = res.data;
			}
		}
	};
</script>

<style lang="scss" scoped>
	.flex47-clz {
		margin-left: 0rpx;
		flex-shrink: 0;
		width: calc(100% - 0rpx - 0rpx) !important;
		margin-top: 12rpx;
		margin-bottom: 10rpx;
		margin-right: 0rpx;
	}
	.text43-clz {
		margin-left: 0rpx;
		color: #050505;
		width: calc(100% - 0rpx - 0rpx) !important;
		font-size: 26rpx !important;
		margin-top: 2rpx;
		margin-bottom: 20rpx;
		margin-right: 0rpx;
	}
	.flex48-clz {
		margin-left: 0rpx;
		margin-top: 0rpx;
		margin-bottom: -2rpx;
		margin-right: 0rpx;
	}
	.text42-clz {
		margin-left: 0rpx;
		color: #756f6f;
		width: calc(100% - 0rpx - 10rpx) !important;
		font-size: 26rpx !important;
		margin-top: 0rpx;
		margin-bottom: 0rpx;
		text-align: left;
		margin-right: 10rpx;
	}
	.container328924 {
	}
</style>
