<template>
	<view class="container container328924">
		<view class="flex flex-wrap diygw-col-24 flex-direction-column">
			<text class="diygw-col-0 text-clz">
				{{ globalData.detail.title }}
			</text>
			<text class="diygw-col-0 text1-clz"> 项目类型: {{ globalData.detail.type }} </text>
			<text class="diygw-col-0 text7-clz"> 我的定位： {{ getMyPositionFunction() }} </text>
			<view class="flex flex-wrap diygw-col-24">
				<text class="diygw-col-0 text4-clz"> 学院要求： </text>
				<view v-for="(item, index) in globalData.detail.academyList" :key="index" class="flex flex-wrap diygw-col-0 flex-direction-column">
					<text class="diygw-col-0 text13-clz">
						{{ item.name }}
					</text>
				</view>
			</view>
			<text class="diygw-col-0 text8-clz"> 创建时间：{{ $tools.formatDateTime(globalData.detail.create_time, 'YYYY-mm-dd HH:MM') }} </text>
			<text class="diygw-col-0 text2-clz"> 结束时间：{{ $tools.formatDateTime(globalData.detail.ending_time, 'YYYY-mm-dd HH:MM') }} </text>
			<view class="flex diygw-col-24 flex-nowrap">
				<text class="diygw-col-0 text3-clz"> 现有成员列表 </text>
				<button @tap="navigateTo" data-type="openmodal" data-id="modal" class="diygw-col-0 btn-clz diygw-btn-default">查看</button>
			</view>
			<mp-html :content="globalData.detail.description" class="diygw-col-0 ucontent-clz"></mp-html>
		</view>
		<view @touchmove.stop.prevent="" @click.stop.prevent="closeModal" v-if="modal" class="diygw-modal bottom-modal" :class="modal" style="z-index: 1000000">
			<view @click.stop.prevent="stopCloseModal" class="diygw-dialog diygw-dialog-modal">
				<view class="justify-end diygw-bar">
					<view class="content"> 成员列表 </view>
				</view>
				<view>
					<view class="flex diygw-dialog-content">
						<view v-for="(item, index) in globalData.detail.memberList" :key="index" class="flex flex-wrap diygw-col-24 flex-direction-column">
							<text class="diygw-col-24 diygw-text-lg">
								{{ item.project_position }}
							</text>
							<text class="diygw-col-24 diygw-text-md">
								{{ item.user.real_name }}
							</text>
						</view>
					</view>
				</view>
				<view class="flex justify-end">
					<button @tap="navigateTo" data-type="page" data-url="/pages/project/member_action" :data-id="globalOption.id" class="diygw-btn green flex1 margin-xs">成员操作</button>
				</view>
			</view>
		</view>
		<view class="clearfix"></view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				//用户全局信息
				userInfo: {},
				//页面传参
				globalOption: {},
				//自定义全局变量
				globalData: { detail: {} },
				ucontent: `<div></div>`,
				modal: ''
			};
		},
		onShow() {
			this.setCurrentPage(this);

			this.initShow();
		},
		onLoad(option) {
			this.setCurrentPage(this);
			if (option) {
				this.setData({
					globalOption: this.getOption(option)
				});
			}

			this.init();
		},
		methods: {
			async init() {
				await this.getDetailFunction();
			},
			async initShow() {
				await this.checkLoginFunction();
			},
			// 获取项目详情 自定义方法
			async getDetailFunction(param) {
				let thiz = this;
				let option = param && (param.option || param.option == 0) ? param.option : thiz.globalOption || '';

				const res = await uniCloud.importObject('Project').getDetail({ id: option.id });
				if (res.status == 0) {
					uni.showToast({
						title: res.msg,
						icon: 'error',
						duration: 2000
					});
					setTimeout(function () {
						uni.redirectTo({
							url: '/pages/home'
						});
					}, 2000);
				}

				this.globalData.detail = res.data;
			},

			// 检查登录 自定义方法
			async checkLoginFunction(param) {
				let thiz = this;
				if (!this.$session.getToken()) {
					//比如未登录，转身到其他页面等
					this.showToast('请先登录');

					this.navigateTo({
						type: 'page',
						url: 'sign/login'
					});
				}
			},

			// 获取我的职位 自定义方法
			getMyPositionFunction(param) {
				let thiz = this;
				const userId = this.$session.getUserValue('user_id');
				for (const i in this.globalData.detail.memberList) {
					if (this.globalData.detail.memberList[i].user._id == userId) return this.globalData.detail.memberList[i].project_position;
				}
				return '游客';
			},
			stopCloseModal(e) {
				e.stopPropagation();
			},
			closeModal() {
				this.modal = '';
			}
		}
	};
</script>

<style lang="scss" scoped>
	.text-clz {
		margin-left: 10rpx;
		font-size: 30rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text1-clz {
		margin-left: 10rpx;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text7-clz {
		margin-left: 10rpx;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text4-clz {
		margin-left: 10rpx;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text13-clz {
		padding-top: 2rpx;
		border-bottom-left-radius: 12rpx;
		padding-left: 10rpx;
		padding-bottom: 10rpx;
		border-top-right-radius: 12rpx;
		margin-right: 10rpx;
		background-color: #e5eae5;
		margin-left: 10rpx;
		flex-shrink: 0;
		overflow: hidden;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 10rpx;
		height: 40rpx !important;
		padding-right: 10rpx;
	}
	.text8-clz {
		margin-left: 10rpx;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text2-clz {
		margin-left: 10rpx;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text3-clz {
		margin-left: 10rpx;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.btn-clz {
		background-color: #ffeffb;
		padding-top: 20rpx;
		color: #aba6a6;
		padding-left: 20rpx;
		padding-bottom: 20rpx;
		text-align: center;
		padding-right: 20rpx;
	}
	.ucontent-clz {
		margin-left: 10rpx;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.modal-clz {
		z-index: 1000000;
	}
	.diygw-dialog-modal {
	}
	.container328924 {
	}
</style>
