<template>
	<view class="container container328924">
		<text class="diygw-col-24 diygw-text-md">
			{{ globalData.detail.title }}
		</text>
		<text class="diygw-col-24"> 主办方：{{ globalData.detail.organizers }} </text>
		<text class="diygw-col-24"> 承办方：{{ globalData.detail.contractors }} </text>
		<text class="diygw-col-24"> 创建时间：{{ $tools.formatDateTime(globalData.detail.create_time, 'YYYY-mm-dd HH:MM') }} </text>
		<text class="diygw-col-24"> 结束时间：{{ $tools.formatDateTime(globalData.detail.ending_time, 'YYYY-mm-dd HH:MM') }} </text>
		<rich-text :nodes="globalData.detail.description" class="diygw-col-24"></rich-text>
		<button v-if="$tools.formatDateTime(globalData.detail.ending_time, 'YYYY-mm-dd HH:MM') > $tools.getCurrentDateTime()" @tap="navigateTo" data-type="page" data-url="/pages/competition/add_project" :data-id="globalData.detail._id" class="diygw-col-24 btn-clz diygw-btn-default">添加竞赛</button>
		<view class="clearfix"></view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				//用户全局信息
				userInfo: {},
				//页面传参
				globalOption: {},
				//自定义全局变量
				globalData: { detail: {} },
				text5: '内容'
			};
		},
		onShow() {
			this.setCurrentPage(this);
		},
		onLoad(option) {
			this.setCurrentPage(this);
			if (option) {
				this.setData({
					globalOption: this.getOption(option)
				});
			}

			this.init();
		},
		methods: {
			async init() {
				await this.getDetailFunction();
			},
			// 获取竞赛详情 自定义方法
			async getDetailFunction(param) {
				let thiz = this;
				let option = param && (param.option || param.option == 0) ? param.option : thiz.globalOption || '';
				const res = await uniCloud.importObject('Competition').getDetail({
					id: option.id
				});
				if (res.status == 0) {
					uni.showToast({
						title: res.msg,
						icon: 'error',
						duration: 2000
					});
					setTimeout(function () {
						uni.redirectTo({
							url: '/pages/home'
						});
					}, 2000);
				}

				this.globalData.detail = res.data;
			},

			// 毫秒timestamp 自定义方法
			async milliSecondFunction(param) {
				let thiz = this;
				const timestampMs = Date.now();
				return timestampMs;
			}
		}
	};
</script>

<style lang="scss" scoped>
	.btn-clz {
		background-color: #07c160;
		padding-top: 20rpx;
		color: #fff;
		padding-left: 20rpx;
		padding-bottom: 20rpx;
		text-align: center;
		padding-right: 20rpx;
	}
	.container328924 {
	}
</style>
