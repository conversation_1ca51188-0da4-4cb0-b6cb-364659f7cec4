{"opendb-admin-menus": [], "opendb-app-list": [], "opendb-app-versions": [], "opendb-banner": [], "opendb-department": [], "opendb-device": [], "opendb-feedback": [], "opendb-frv-logs": [], "opendb-news-articles": [], "opendb-news-categories": [], "opendb-news-comments": [], "opendb-news-favorite": [], "opendb-open-data": [], "opendb-poi": [], "opendb-search-hot": [], "opendb-search-log": [], "opendb-sign-in": [], "opendb-sms-log": [], "opendb-sms-task": [], "opendb-sms-template": [], "opendb-tempdata": [], "opendb-verify-codes": [], "read-news-log": {"schema": {"bsonType": "object", "required": ["user_id", "article_id"], "permission": {"read": "doc.user_id == auth.uid", "create": "auth.uid != null", "update": "doc.user_id == auth.uid"}, "properties": {"_id": {"description": "ID，系统自动生成"}, "article_id": {"bsonType": "string", "description": "文章id，参考opendb-news-articles表", "foreignKey": "opendb-news-articles._id"}, "user_id": {"bsonType": "string", "description": "收藏者id，参考uni-id-users表", "forceDefaultValue": {"$env": "uid"}, "foreignKey": "uni-id-users._id"}, "last_time": {"bsonType": "timestamp", "description": "最后一次看的时间", "defaultValue": {"$env": "now"}}}}}, "uni-id-device": [], "uni-id-log": [], "uni-id-permissions": [], "uni-id-roles": [], "uni-id-scores": [], "uni-id-tag": [], "uni-id-users": [], "uni-pay-orders": [], "uni-stat-active-devices": [], "uni-stat-active-users": [], "uni-stat-app-channels": [], "uni-stat-app-crash-logs": [], "uni-stat-app-platforms": [], "uni-stat-app-versions": [], "uni-stat-error-logs": [], "uni-stat-error-result": [], "uni-stat-error-source-map": [], "uni-stat-event-logs": [], "uni-stat-event-result": [], "uni-stat-events": [], "uni-stat-loyalty-result": [], "uni-stat-mp-scenes": [], "uni-stat-page-detail-result": [], "uni-stat-page-details": [], "uni-stat-page-logs": [], "uni-stat-page-result": [], "uni-stat-pages": [], "uni-stat-pay-result": [], "uni-stat-result": [], "uni-stat-run-errors": [], "uni-stat-session-logs": [], "uni-stat-share-logs": [], "uni-stat-user-session-logs": [], "user": {"data": [{"username": "葛耘", "uploaded_project": 0, "pending_invited_project": 0, "invited_project_history": 0, "create_time": "2024-10-15 02:18:48.0000", "password": "abc12345"}], "schema": {"bsonType": "object", "required": [], "permission": {"read": false, "create": false, "update": false, "delete": false}, "properties": {"_id": {"description": "ID，系统自动生成"}}}}}