<template>
	<view class="container container328924">
		<view class="flex flex-wrap diygw-col-24 flex-direction-column">
			<view class="flex diygw-col-24">
				<view class="diygw-search">
					<view class="flex1 align-center flex padding-xs solid radius">
						<text style="color: #555 !important" class="diy-icon-search"></text>
						<input class="flex1" name="search" type="" v-model="search" placeholder="请输入名字" />
					</view>
					<view @tap="navigateTo" data-type="searchFunction" style="color: #333 !important" class="diygw-tag margin-left-xs radius-xs"> 搜索 </view>
				</view>
			</view>
			<view class="flex tabs diygw-col-24 flex-direction-column">
				<view class="diygw-tabs text-center solid-bottom justify-center scale-title small-border tabs-title">
					<view class="diygw-tab-item tabs-item-title" :class="index == tabsIndex ? '    cur text-green  ' : ''" v-for="(item, index) in tabsDatas" :key="index" @click="changeTabs" :data-index="index"> <text v-if="item.icon" :class="item.icon"></text> {{ item.text }} </view>
				</view>
				<view class="">
					<view v-if="tabsIndex == 0" class="flex-sub">
						<view v-for="(item, index) in globalData.list[0]" :key="index" class="flex flex-wrap diygw-col-24 flex-direction-column">
							<view class="flex flex-wrap diygw-col-24 flex-direction-column flex6-clz" @tap="navigateTo" data-type="page" data-url="/pages/user/project" :data-user_id="item._id">
								<text class="diygw-col-24 text6-clz"> {{ item.real_name }}（{{ item.onboarding_year }}级 {{ item.type }}） </text>
								<text class="diygw-col-24 text7-clz">
									{{ item.college }}
								</text>
								<text class="diygw-col-24 text8-clz"> 自身项目：{{ item.self_count ?? 0 }}, 加入项目：{{ item.request_count ?? 0 }}, 受邀请项目：{{ item.invited_count ?? 0 }} </text>
							</view>
						</view>
					</view>
					<view v-if="tabsIndex == 1" class="flex-sub">
						<view v-for="(item, index) in globalData.list[1]" :key="index" class="flex flex-wrap diygw-col-24 flex-direction-column">
							<view class="flex flex-wrap diygw-col-24 flex-direction-column flex4-clz" @tap="navigateTo" data-type="page" data-url="/pages/user/project" :data-user_id="item._id">
								<text class="diygw-col-24 text3-clz"> {{ item.real_name }}（{{ item.onboarding_year }}级 {{ item.type }}） </text>
								<text class="diygw-col-24 text4-clz">
									{{ item.college }}
								</text>
								<text class="diygw-col-24 text5-clz"> 自身项目：{{ item.self_count ?? 0 }}, 加入项目：{{ item.request_count ?? 0 }}, 受邀请项目：{{ item.invited_count ?? 0 }} </text>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="clearfix"></view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				//用户全局信息
				userInfo: {},
				//页面传参
				globalOption: {},
				//自定义全局变量
				globalData: { backup_list: [null, null], list: [null, null] },
				search: '',
				tabsDatas: [
					{ text: `学生`, icon: `` },
					{ text: `导师`, icon: `` }
				],
				tabsLeft: 0,
				tabsWidth: 0,
				tabsItemWidth: 0,
				tabsIndex: 0
			};
		},
		onShow() {
			this.setCurrentPage(this);
		},
		onLoad(option) {
			this.setCurrentPage(this);
			if (option) {
				this.setData({
					globalOption: this.getOption(option)
				});
			}

			this.init();
		},
		methods: {
			async init() {
				await this.checkLoginFunction();
				await this.defaultLoadFunction();
			},
			// 获取用户列表 自定义方法
			async getListFunction(param) {
				let thiz = this;
				if (this.globalData.backup_list[thiz.tabsIndex] !== null) return;

				const res = await uniCloud.importObject('User').getList({ type: ['学生', '导师'][thiz.tabsIndex], user_id: this.$session.getUserValue('user_id') });
				this.globalData.list[thiz.tabsIndex] = res.data;
				this.globalData.backup_list[thiz.tabsIndex] = res.data;
			},

			// 检查登录 自定义方法
			async checkLoginFunction(param) {
				let thiz = this;
				if (!this.$session.getToken()) {
					//比如未登录，转身到其他页面等
					this.showToast('请先登录');

					this.navigateTo({
						type: 'page',
						url: 'sign/login'
					});
				}
			},

			// 默认获取 自定义方法
			async defaultLoadFunction(param) {
				let thiz = this;
				this.getListFunction();
			},

			// 搜索用户 自定义方法
			async searchFunction(param) {
				let thiz = this;
				if (this.globalData.backup_search != '' && this.globalData.backup_search == this.search) {
					return;
				}

				if (this.search == '') {
					this.globalData.list[thiz.tabsIndex] = this.globalData.backup_list[thiz.tabsIndex];
					this.globalData.backup_search = '';
					return;
				}

				const res = await uniCloud.importObject('User').getList({ type: ['学生', '导师'][thiz.tabsIndex], user_id: this.$session.getUserValue('user_id'), real_name: this.search });
				this.globalData.list[thiz.tabsIndex] = res.data;
				this.globalData.backup_search = this.search;
			},
			changeTabs(evt) {
				let { index } = evt.currentTarget.dataset;
				if (index == this.tabsIndex) return;
				this.setData({
					tabsIndex: index
				});
				this.navigateTo({ type: 'getListFunction' });
			}
		}
	};
</script>

<style lang="scss" scoped>
	.flex6-clz {
		margin-left: 10rpx;
		border: 2rpx solid #aba6a6;
		border-bottom-left-radius: 12rpx;
		overflow: hidden;
		width: calc(100% - 10rpx - 10rpx) !important;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-top-right-radius: 12rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text6-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text7-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text8-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.flex4-clz {
		margin-left: 10rpx;
		border: 2rpx solid #aba6a6;
		border-bottom-left-radius: 12rpx;
		overflow: hidden;
		width: calc(100% - 10rpx - 10rpx) !important;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-top-right-radius: 12rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text3-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text4-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text5-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.container328924 {
	}
</style>
