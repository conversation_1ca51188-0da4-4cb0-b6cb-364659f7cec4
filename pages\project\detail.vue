<template>
	<view class="container container328924">
		<view class="flex diygw-col-24 flex-direction-column flex-nowrap flex18-clz">
			<text @tap="navigateTo" data-type="showProjectDetailFunction" class="diygw-col-0 text5-clz">
				{{ globalData.detail.title }}
			</text>
			<text class="diygw-col-0 text2-clz"> 项目类型：{{ globalData.detail.type }} </text>
			<text v-if="globalData.detail.comp_name" class="diygw-col-0 text9-clz"> 竞赛：{{ globalData.detail.comp_name }} </text>
			<text class="diygw-col-0 text7-clz"> 我的定位： {{ getMyPositionFunction() }} </text>
			<view class="flex flex-wrap diygw-col-24">
				<text class="diygw-col-0 text4-clz"> 学院要求： </text>
				<view v-for="(item, index) in globalData.detail.academyList" :key="index" class="flex flex-wrap diygw-col-0 flex-direction-column">
					<text class="diygw-col-0 text13-clz">
						{{ item.name }}
					</text>
				</view>
			</view>
			<view class="flex flex-wrap diygw-col-0">
				<text class="diygw-col-0 text18-clz"> 招募人数： {{ globalData.detail.person_needed }} 人 </text>
				<text class="diygw-col-0 text1-clz"> 已加入： {{ globalData.detail.current_person_request ?? 0 }} </text>
			</view>
			<view class="flex diygw-col-0 flex-nowrap flex-clz">
				<view class="flex flex-wrap diygw-col-24 flex-direction-column">
					<text class="diygw-col-0 text23-clz"> 创建时间： {{ $tools.formatDateTime(globalData.detail.create_time, 'YYYY-mm-dd HH:MM') }} </text>
					<text class="diygw-col-0 text-clz"> 结束时间： {{ $tools.formatDateTime(globalData.detail.ending_time, 'YYYY-mm-dd HH:MM') }} </text>
				</view>
			</view>
		</view>
		<view class="flex diygw-col-24 flex-nowrap">
			<text class="diygw-col-0 text3-clz"> 现有成员列表 </text>
			<button @tap="navigateTo" data-type="openmodal" data-id="modal" class="diygw-col-0 btn-clz diygw-btn-default">查看</button>
		</view>
		<mp-html :content="globalData.detail.description" class="diygw-col-24"></mp-html>
		<button v-if="globalData.detail.pending != 1 && globalData.detail.ending_time > secondFunction()" @tap="navigateTo" data-type="openmodal" data-id="modal1" class="diygw-col-24 btn1-clz diygw-btn-default">申请加入</button>
		<view @touchmove.stop.prevent="" v-if="modal" class="diygw-modal bottom-modal" :class="modal" style="z-index: 1000000">
			<view class="diygw-dialog diygw-dialog-modal">
				<view class="justify-end diygw-bar">
					<view class="content"> 成员列表 </view>
				</view>
				<view>
					<view class="flex diygw-dialog-content">
						<view v-for="(item, index) in globalData.detail.memberList" :key="index" class="flex flex-wrap diygw-col-24 flex-direction-column">
							<text class="diygw-col-24 diygw-text-lg">
								{{ item.project_position }}
							</text>
							<text class="diygw-col-24 diygw-text-md">
								{{ item.user.real_name }}
							</text>
						</view>
					</view>
				</view>
				<view class="flex justify-end">
					<button data-type="closemodal" @tap="navigateTo" data-id="modal" class="diygw-btn green flex1 margin-xs">关闭</button>
				</view>
			</view>
		</view>
		<view @touchmove.stop.prevent="" v-if="modal1" class="diygw-modal basic" :class="modal1" style="z-index: 1000000">
			<view class="diygw-dialog diygw-dialog-modal1 basis-lg">
				<view class="justify-end diygw-bar">
					<view class="content"> 申请加入 </view>
					<view class="action" data-type="closemodal" data-id="modal1" @tap="navigateTo">
						<text class="diy-icon-close"></text>
					</view>
				</view>
				<view>
					<view class="flex diygw-dialog-content">
						<u-form-item class="diygw-col-24" labelPosition="top" prop="self_introduce">
							<u-input maxlength="200" height="60px" class="" placeholder="自我介绍（请介绍自己以提升被选中的可能性）" v-model="self_introduce" type="textarea"></u-input>
						</u-form-item>
					</view>
				</view>
				<view class="flex justify-end">
					<button @tap="navigateTo" data-type="requestToJoinFunction" class="diygw-btn green flex1 margin-xs">申请</button>
				</view>
			</view>
		</view>
		<button v-show="!(globalData.detail.ending_time > secondFunction())" class="diygw-col-24 btn2-clz diygw-btn-default">已过期</button>
		<view class="clearfix"></view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				//用户全局信息
				userInfo: {},
				//页面传参
				globalOption: {},
				//自定义全局变量
				globalData: { detail: {} },
				modal: '',
				modal1: '',
				self_introduce: ''
			};
		},
		onShow() {
			this.setCurrentPage(this);

			this.initShow();
		},
		onLoad(option) {
			this.setCurrentPage(this);
			if (option) {
				this.setData({
					globalOption: this.getOption(option)
				});
			}

			this.init();
		},
		methods: {
			async init() {
				await this.getProjectDetailFunction();
			},
			async initShow() {
				await this.checkLoginFunction();
			},
			// 获取项目详情 自定义方法
			async getProjectDetailFunction(param) {
				let thiz = this;
				let option = param && (param.option || param.option == 0) ? param.option : thiz.globalOption || '';
				const res = await uniCloud.importObject('project').getDetail({ id: option.id, user_id: this.$session.getUserValue('user_id') });

				if (!res.status) {
					uni.redirectTo({
						url: '/'
					});
				}

				this.globalData.detail = res.data;
			},

			// 获取我的职位 自定义方法
			getMyPositionFunction(param) {
				let thiz = this;
				if (this.globalData.detail.pending) return '等待接受';
				const userId = this.$session.getUserValue('user_id');

				for (const i in this.globalData.detail.memberList) {
					if (this.globalData.detail.memberList[i].user._id == userId) return this.globalData.detail.memberList[i].project_position;
				}
				return '游客';
			},

			// 检查登录 自定义方法
			async checkLoginFunction(param) {
				let thiz = this;
				if (!this.$session.getToken()) {
					//比如未登录，转身到其他页面等
					this.showToast('请先登录');

					this.navigateTo({
						type: 'page',
						url: 'sign/login'
					});
				}
			},

			// 申请加入 自定义方法
			async requestToJoinFunction(param) {
				let thiz = this;
				const res = await uniCloud.importObject('ProjectAction').requestJoin({
					project_id: this.globalData.detail._id,
					introduce: this.self_introduce,
					user_id: this.$session.getUserValue('user_id')
				});

				uni.showToast({
					icon: res.status ? 'success' : 'error',
					title: res.msg
				});

				if (res.status == 1) {
					this.navigateTo({
						type: 'closemodal',
						id: 'modal1'
					});
				}
			},

			// 秒timestamp 自定义方法
			secondFunction(param) {
				let thiz = this;
				const timestampMs = Date.now();
				const timestampSeconds = Math.floor(timestampMs / 1000);
				return timestampSeconds;
			}
		}
	};
</script>

<style lang="scss" scoped>
	.flex18-clz {
		flex: 1;
		border-bottom: 2rpx solid #eee;
	}
	.text5-clz {
		margin-left: 10rpx;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text2-clz {
		margin-left: 10rpx;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text9-clz {
		margin-left: 10rpx;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text7-clz {
		margin-left: 10rpx;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text4-clz {
		margin-left: 10rpx;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text13-clz {
		padding-top: 2rpx;
		border-bottom-left-radius: 12rpx;
		padding-left: 10rpx;
		padding-bottom: 10rpx;
		border-top-right-radius: 12rpx;
		margin-right: 10rpx;
		background-color: #e5eae5;
		margin-left: 10rpx;
		flex-shrink: 0;
		overflow: hidden;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 10rpx;
		height: 40rpx !important;
		padding-right: 10rpx;
	}
	.text18-clz {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 80rpx;
	}
	.text1-clz {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 80rpx;
	}
	.flex-clz {
		flex: 1;
	}
	.text23-clz {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text-clz {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text3-clz {
		margin-left: 10rpx;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.btn-clz {
		background-color: #ffeffb;
		padding-top: 20rpx;
		color: #aba6a6;
		padding-left: 20rpx;
		padding-bottom: 20rpx;
		text-align: center;
		padding-right: 20rpx;
	}
	.btn1-clz {
		background-color: #07c160;
		padding-top: 20rpx;
		color: #fff;
		left: 0rpx;
		bottom: 0rpx;
		padding-left: 20rpx;
		padding-bottom: 20rpx;
		position: absolute;
		text-align: center;
		padding-right: 20rpx;
	}
	.modal-clz {
		z-index: 1000000;
	}
	.diygw-dialog-modal {
	}
	.modal1-clz {
		z-index: 1000000;
	}
	.diygw-dialog-modal1 {
	}
	.btn2-clz {
		background-color: #b9bfbb;
		padding-top: 20rpx;
		color: #050505;
		padding-left: 20rpx;
		padding-bottom: 20rpx;
		text-align: center;
		padding-right: 20rpx;
	}
	.container328924 {
	}
</style>
