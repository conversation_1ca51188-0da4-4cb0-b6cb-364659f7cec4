<template>
	<view class="container container328924">
		<view class="flex flex-wrap diygw-col-24 justify-between items-center">
			<rich-text :nodes="text" class="diygw-col-0 text-clz"></rich-text>
			<view class="flex flex-wrap diygw-col-0 flex-direction-column items-center flex11-clz" @tap="navigateTo" data-type="page" data-url="/pages/project/add">
				<text class="flex icon diygw-col-0 icon-clz diy-icon-roundadd"></text>
				<text class="diygw-col-0 text21-clz"> 新增项目 </text>
			</view>
		</view>
		<view v-for="(item, index) in globalData.list" :key="index" class="flex flex-wrap diygw-col-24 flex-direction-column justify-center items-center">
			<view class="flex flex-wrap diygw-col-24 flex5-clz">
				<view class="flex flex-wrap diygw-col-12 flex-direction-column">
					<text class="diygw-col-0 text1-clz">
						{{ item.title }}
					</text>
					<text class="diygw-col-0 text2-clz"> 项目类型：{{ item.type_id }} </text>
					<text class="diygw-col-24 text3-clz"> 申请者： 8 人 </text>
					<text class="diygw-col-24 text4-clz"> 加入者： 2 人 </text>
					<text class="diygw-col-24 text6-clz"> 创建时间： {{ $tools.formatDateTime(item.create_time, 'YYYY-mm-dd HH:MM') }} </text>
					<text class="diygw-col-24 text7-clz"> 结束时间： {{ $tools.formatDateTime(item.ending_time, 'YYYY-mm-dd HH:MM') }} </text>
				</view>
				<view class="flex flex-wrap diygw-col-12 items-start">
					<view class="flex flex-wrap diygw-col-24 flex-direction-row-reverse">
						<text class="diygw-col-0 text5-clz">
							{{ item.status }}
						</text>
					</view>
					<view class="flex flex-wrap diygw-col-24 flex-direction-row-reverse flex15-clz">
						<text @tap="navigateTo" data-type="page" data-url="/pages/project/edit" :data-id="item._id" class="diygw-col-0 text26-clz"> 更新 </text>
						<text @tap="navigateTo" data-type="page" data-url="/pages/project/detail" :data-id="item._id" class="diygw-col-0 text11-clz"> 查看 </text>
					</view>
				</view>
			</view>
		</view>
		<view class="flex diygw-col-24 diygw-bottom">
			<view class="diygw-grid diygw-actions">
				<button @tap="navigateTo" data-type="page" data-url="/pages/home" class="diygw-action">
					<view class="diygw-grid-inner">
						<view class="diygw-grid-icon diygw-avatar" style="">
							<image mode="aspectFit" class="diygw-avatar-img" src="/static/global/grid1.png"></image>
						</view>
						<view class="diygw-grid-title"> 首页 </view>
					</view>
				</button>
				<button @tap="navigateTo" data-type="page" data-url="/pages/project/main_page" class="diygw-action">
					<view class="diygw-grid-inner">
						<view class="diygw-grid-icon diygw-avatar" style="">
							<image mode="aspectFit" class="diygw-avatar-img" src="/static/icon3_dd.png"></image>
						</view>
						<view class="diygw-grid-title"> 项目 </view>
					</view>
				</button>
				<button @tap="navigateTo" data-type="page" data-url="/pages/message/main_page" class="diygw-action">
					<view class="diygw-grid-inner">
						<view class="diygw-grid-icon diygw-avatar" style="">
							<image mode="aspectFit" class="diygw-avatar-img" src="/static/icon3_xx.png"></image>
						</view>
						<view class="diygw-grid-title"> 消息 </view>
					</view>
				</button>
				<button @tap="navigateTo" data-type="page" data-url="/pages/profile/main_page" class="diygw-action">
					<view class="diygw-grid-inner">
						<view class="diygw-grid-icon diygw-avatar" style="">
							<image mode="aspectFit" class="diygw-avatar-img" src="/static/icon1_rzyh.png"></image>
						</view>
						<view class="diygw-grid-title"> 我 </view>
					</view>
				</button>
			</view>
		</view>
		<view class="clearfix"></view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				//用户全局信息
				userInfo: {},
				//页面传参
				globalOption: {},
				//自定义全局变量
				globalData: { list: [] },
				text: `<p><span style="font-size: 18px;">我发起的项目</span></p>`
			};
		},
		onShow() {
			this.setCurrentPage(this);

			this.initShow();
		},
		onLoad(option) {
			this.setCurrentPage(this);
			if (option) {
				this.setData({
					globalOption: this.getOption(option)
				});
			}

			this.init();
		},
		methods: {
			async init() {
				await this.getProjectListFunction();
			},
			async initShow() {
				await this.checkLoginFunction();
			},
			// 获取自己的项目列表 自定义方法
			async getProjectListFunction(param) {
				let thiz = this;
				const res = await uniCloud.importObject('Project').getSelf({ user_id: this.$session.getUserValue('user_id') });

				this.globalData.list = res.data;
			},

			// 检查登录 自定义方法
			async checkLoginFunction(param) {
				let thiz = this;
				if (!this.$session.getToken()) {
					//比如未登录，转身到其他页面等
					this.showToast('请先登录');

					this.navigateTo({
						type: 'page',
						url: 'sign/login'
					});
				}
			}
		}
	};
</script>

<style lang="scss" scoped>
	.text-clz {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.flex11-clz {
		margin-left: 10rpx;
		margin-top: 4rpx;
		margin-bottom: 0rpx;
		margin-right: 14rpx;
	}
	.icon-clz {
		margin-left: 10rpx;
		margin-top: 0rpx;
		margin-bottom: 0rpx;
		margin-right: 10rpx;
	}
	.icon {
		font-size: 48rpx;
	}
	.text21-clz {
		font-size: 22rpx !important;
	}
	.flex5-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text1-clz {
		margin-left: 10rpx;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text2-clz {
		margin-left: 10rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text3-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text4-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text6-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text7-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text5-clz {
		padding-top: 2rpx;
		border-bottom-left-radius: 12rpx;
		padding-left: 10rpx;
		padding-bottom: 10rpx;
		border-top-right-radius: 12rpx;
		margin-right: 10rpx;
		background-color: #e5eae5;
		margin-left: 10rpx;
		flex-shrink: 0;
		overflow: hidden;
		border-top-left-radius: 12rpx;
		margin-top: 10rpx;
		border-bottom-right-radius: 12rpx;
		margin-bottom: 10rpx;
		height: 40rpx !important;
		padding-right: 10rpx;
	}
	.flex15-clz {
		margin-left: 2rpx;
		flex-shrink: 0;
		left: 0rpx;
		bottom: 0rpx;
		width: calc(100% - 2rpx - 0rpx) !important;
		margin-top: 0rpx;
		position: absolute;
		margin-bottom: 6rpx;
		height: 60rpx !important;
		margin-right: 0rpx;
	}
	.text26-clz {
		margin-left: 10rpx;
		border: 2rpx solid #aba6a6;
		flex-shrink: 0;
		padding-top: 4rpx;
		padding-left: 20rpx;
		width: 96rpx !important;
		padding-bottom: 4rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		height: 46rpx !important;
		margin-right: 10rpx;
		padding-right: 12rpx;
	}
	.text11-clz {
		margin-left: 10rpx;
		border: 2rpx solid #aba6a6;
		flex-shrink: 0;
		padding-top: 4rpx;
		padding-left: 20rpx;
		width: 96rpx !important;
		padding-bottom: 4rpx;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		height: 46rpx !important;
		margin-right: 10rpx;
		padding-right: 10rpx;
	}
	.container328924 {
		padding-bottom: 80px;
	}
</style>
