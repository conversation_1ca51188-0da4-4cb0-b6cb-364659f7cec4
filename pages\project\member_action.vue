<template>
	<view class="container container328924">
		<view class="flex flex-wrap diygw-col-24 flex-direction-column">
			<text class="diygw-col-24 text7-clz"> 项目名 </text>
		</view>
		<view class="flex flex-direction-column flex-sub diygw-col-24">
			<view class="flex flex-direction-column flex-sub">
				<view @tap="changeCollapse1" data-index="0" class="diygw-text-md padding flex solid-bottom" :class="[collapse1Datas[0].isShow ? 'text- cur ' : '']">
					<view class="flex-sub"> 内定成员 ({{ getConfirm.length }}) </view>
					<i class="diy-icon-unfold diy-collapse-icon" :class="[collapse1Datas[0].isShow ? 'diy-collapse-active' : '']"></i>
				</view>
				<view v-if="collapse1Datas[0].isShow" class="solid-bottom">
					<view v-for="(value, index) in getConfirm" :key="index" class="flex flex-wrap diygw-col-24 justify-between">
						<view class="flex flex-wrap diygw-col-0 flex-direction-column flex-clz">
							<text class="diygw-col-24 text-clz"> {{ value.user.real_name }} （{{ value.project_position }}） </text>
							<text v-if="value.project_position != '指导老师'" class="diygw-col-24 text1-clz"> {{ value.user.onboarding_year }}级 {{ value.user.type }} </text>
							<text class="diygw-col-24 text2-clz"> {{ value.user.college_category_id }} - {{ value.user.specific_category_id }} </text>
						</view>
						<view v-if="!value.project_position || (value.project_position && !value.project_position.includes('项目负责人', '指导老师'))" class="flex diygw-col-0 flex-direction-column button-clz">
							<button @tap="navigateTo" data-type="changeStatusFunction" :data-data="value" :data-index="index" data-from="confirm" data-to="pending" class="diygw-btn red radius-sm flex-sub margin-xs button-button-clz"><text class="button-icon diy-icon-close"></text> 改回待定</button>
						</view>
					</view>
				</view>
			</view>
			<view class="flex flex-direction-column flex-sub">
				<view @tap="changeCollapse1" data-index="1" class="diygw-text-md padding flex solid-bottom" :class="[collapse1Datas[1].isShow ? 'text- cur ' : '']">
					<view class="flex-sub"> 待定成员({{ getPending.length }}) </view>
					<i class="diy-icon-unfold diy-collapse-icon" :class="[collapse1Datas[1].isShow ? 'diy-collapse-active' : '']"></i>
				</view>
				<view v-if="collapse1Datas[1].isShow" class="solid-bottom">
					<view v-for="(value, index) in getPending" :key="index" class="flex flex-wrap diygw-col-24 justify-between">
						<view class="flex flex-wrap diygw-col-0 flex-direction-column flex1-clz" @tap="navigateTo" data-type="openIntroductionFunction" :data-comment="value.comment">
							<text class="diygw-col-24 text4-clz">
								{{ value.user.real_name }}
							</text>
							<text class="diygw-col-24 text5-clz"> {{ value.user.onboarding_year }}级 {{ value.user.type }} </text>
							<text class="diygw-col-24 text6-clz"> {{ value.user.college_category_id }} - {{ value.user.specific_category_id }} </text>
						</view>
						<view class="flex diygw-col-0 flex-direction-column button2-clz">
							<button @tap="navigateTo" data-type="changeStatusFunction" :data-data="value" :data-index="index" data-from="pending" data-to="confirm" class="diygw-btn cyan radius-sm flex-sub margin-xs button2-button-clz"><text class="button-icon diy-icon-star"></text> 设为内定</button>
							<button @tap="navigateTo" data-type="changeStatusFunction" :data-data="value" :data-index="index" data-from="pending" data-to="unpicked" class="diygw-btn red radius-sm flex-sub margin-xs button2-button-clz"><text class="button-icon diy-icon-close"></text> 取消待定</button>
						</view>
					</view>
				</view>
			</view>
			<view class="flex flex-direction-column flex-sub">
				<view @tap="changeCollapse1" data-index="2" class="diygw-text-md padding flex solid-bottom" :class="[collapse1Datas[2].isShow ? 'text- cur ' : '']">
					<view class="flex-sub"> 还未选上({{ getUnpicked.length }}) </view>
					<i class="diy-icon-unfold diy-collapse-icon" :class="[collapse1Datas[2].isShow ? 'diy-collapse-active' : '']"></i>
				</view>
				<view v-if="collapse1Datas[2].isShow" class="solid-bottom">
					<view v-for="(value, index) in getUnpicked" :key="index" class="flex flex-wrap diygw-col-24 justify-between">
						<view class="flex flex-wrap diygw-col-0 flex-direction-column flex19-clz" @tap="navigateTo" data-type="openIntroductionFunction" :data-comment="value.comment">
							<text class="diygw-col-24 text22-clz">
								{{ value.user.real_name }}
							</text>
							<text class="diygw-col-24 text23-clz"> {{ value.user.onboarding_year }}级 {{ value.user.type }} </text>
							<text class="diygw-col-24 text24-clz"> {{ value.user.college_category_id }} - {{ value.user.specific_category_id }} </text>
						</view>
						<view class="flex diygw-col-0 flex-direction-column button1-clz">
							<button @tap="navigateTo" data-type="changeStatusFunction" :data-data="value" :data-index="index" data-from="unpicked" data-to="pending" class="diygw-btn cyan radius-sm flex-sub margin-xs button1-button-clz"><text class="button-icon diy-icon-star"></text> 选为待定</button>
						</view>
					</view>
				</view>
			</view>
		</view>
		<view class="flex flex-wrap diygw-col-24 flex-direction-column diygw-bottom">
			<button v-if="Object.keys(globalData.changedList).length" @tap="navigateTo" data-type="openmodal" data-id="modal1" class="diygw-col-24 btn-clz diygw-btn-default">人员选择更新</button>
		</view>
		<view @touchmove.stop.prevent="" @click.stop.prevent="closeModal1" v-if="modal1" class="diygw-modal basic" :class="modal1" style="z-index: 1000000">
			<view @click.stop.prevent="stopCloseModal1" class="diygw-dialog diygw-dialog-modal1 basis-lg">
				<view class="justify-end diygw-bar">
					<view class="content"> 是否对以下成员进行更该？ </view>
				</view>
				<view>
					<view class="flex diygw-dialog-content">
						<view v-for="(item, index) in globalData.changedList" :key="index" class="flex flex-wrap diygw-col-24 flex-direction-column">
							<rich-text :nodes="text3" class="diygw-col-24"></rich-text>
						</view>
					</view>
				</view>
				<view class="flex justify-end">
					<button data-type="closemodal" @tap="navigateTo" data-id="modal1" class="diygw-btn red flex1 margin-xs">取消</button>
					<button @tap="navigateTo" data-type="updateMemberFunction" :data-option="globalOption" class="diygw-btn green flex1 margin-xs">确定</button>
				</view>
			</view>
		</view>
		<view @touchmove.stop.prevent="" @click.stop.prevent="closeModal" v-if="modal" class="diygw-modal bottom-modal" :class="modal" style="z-index: 1000000">
			<view @click.stop.prevent="stopCloseModal" class="diygw-dialog diygw-dialog-modal">
				<view class="justify-end diygw-bar">
					<view class="content"> 自我介绍 </view>
				</view>
				<view>
					<view class="flex diygw-dialog-content">
						<mp-html :content="globalData.introduction" class="diygw-col-24 ucontent1-clz"></mp-html>
					</view>
				</view>
			</view>
		</view>
		<view class="clearfix"></view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				//用户全局信息
				userInfo: {},
				//页面传参
				globalOption: {},
				//自定义全局变量
				globalData: { changedList: {}, list: { request: [], relation: [] }, introduction: '', defaultList: {} },
				collapse1Datas: [
					{ text: '内定成员 ({{getConfirm.length}})', isShow: true },
					{ text: '待定成员({{getPending.length}})', isShow: true },
					{ text: '还未选上({{getUnpicked.length }})', isShow: true }
				],
				modal1: '',
				text3: `<p style="text-align: center;">名字：{{item.default.user.real_name}}</p>
<p style="text-align: center;">身份: {{item.default.user.onboarding_year}}级别 {{item.default.user.type}}</p>
<p style="text-align: center;">学院：{{item.default.user.college_category_id}}-{{item.default.user.specific_category_id}}</p>
<p style="text-align: center;">从 <span style="color: #3598db;">还未选上</span> 设置成 <span style="color: #3598db;">内定成员</span></p>`,
				modal: ''
			};
		},
		computed: {
			getConfirm() {
				const list = new Map(); // 用 Map 去重
				this.globalData.list.relation.filter((value) => value.project_position != '待定成员').forEach((item) => list.set(item._id, { ...item, default: 'confirm' }));

				for (const id in this.globalData.changedList) {
					const changed = this.globalData.changedList[id];
					if (changed.to === 'confirm') {
						list.set(changed.default._id, changed.default);
					}
				}

				return Array.from(list.values());

				// const list = this.globalData.list.relation.filter((value) => {
				// 	return value.project_position != '待定成员';
				// });

				// for (const i in list) list[i].default = 'confirm';

				// for (const id in this.globalData.changedList) {
				// 	if (this.globalData.changedList[id].to == 'confirm')
				// 	list.push(JSON.parse(JSON.stringify(this.globalData.changedList[id].default)));
				// }

				// return list;
			},
			getPending() {
				const list = new Map(); // 用 Map 去重
				this.globalData.list.relation.filter((value) => value.project_position == '待定成员').forEach((item) => list.set(item._id, { ...item, default: 'pending' }));

				for (const id in this.globalData.changedList) {
					const changed = this.globalData.changedList[id];
					if (changed.to === 'pending') {
						list.set(changed.default._id, changed.default);
					}
				}

				return Array.from(list.values());

				// const list = this.globalData.list.relation.filter((value) => {
				// 	return value.project_position == '待定成员';
				// });

				// for (const i in list) list[i].default = 'pending';

				// for (const id in this.globalData.changedList) {
				// 	if (this.globalData.changedList[id].to == 'pending') {
				// 		list.push(JSON.parse(JSON.stringify(this.globalData.changedList[id].default)));
				// 	}
				// }

				// return list;
			},
			getUnpicked() {
				const list = new Map(); // 用 Map 去重
				this.globalData.list.request.forEach((item) => list.set(item._id, { ...item, default: 'unpicked' }));

				for (const id in this.globalData.changedList) {
					const changed = this.globalData.changedList[id];
					if (changed.to === 'unpicked') {
						list.set(changed.default._id, changed.default);
					}
				}

				return Array.from(list.values());

				// const list = this.globalData.list.request;
				// for (const i in list) list[i].default = 'unpicked';

				// for (const id in this.globalData.changedList) {
				// 	if (this.globalData.changedList[id].to == 'unpicked')

				// 	list.push(JSON.parse(JSON.stringify(this.globalData.changedList[id].default)));
				// }

				// return list;
			}
		},
		onShow() {
			this.setCurrentPage(this);
		},
		onLoad(option) {
			this.setCurrentPage(this);
			if (option) {
				this.setData({
					globalOption: this.getOption(option)
				});
			}

			this.init();
		},
		methods: {
			async init() {
				await this.getMembersFunction();
			},
			// 获取该项目的人员 自定义方法
			async getMembersFunction(param) {
				let thiz = this;
				let option = param && (param.option || param.option == 0) ? param.option : thiz.globalOption || '';
				const res = await uniCloud.importObject('Project').getProjectMembers({ id: option.id, user_id: this.$session.getUserValue('user_id') });

				if (!res.status) {
					uni.showToast({
						title: res.msg,
						icon: 'error',
						duration: 2000
					});
					setTimeout(function () {
						uni.redirectTo({
							url: '/pages/in_project/self'
						});
					}, 2000);
				}

				this.globalData.list = res.data;

				const tempList = this.globalData.list.user;
				const userList = {};
				for (const i in tempList) userList[tempList[i]._id] = tempList[i];

				for (const i in this.globalData.list.relation) {
					this.globalData.list.relation[i].user = userList[this.globalData.list.relation[i].user_id];

					for (const j in this.globalData.list.invited) {
						if (this.globalData.list.relation[i].user_id == this.globalData.list.invited[j].user_id) this.globalData.list.relation[i].isInvited = 1;
					}
				}

				for (const i in this.globalData.list.request) {
					this.globalData.list.request[i].user = userList[this.globalData.list.request[i].user_id];
				}

				this.globalData.defaultList = { ...this.globalData.list };
			},

			// 开启自我介绍 自定义方法
			async openIntroductionFunction(param) {
				let thiz = this;
				let comment = param && (param.comment || param.comment == 0) ? param.comment : '';
				this.globalData.introduction = comment;

				this.navigateTo({
					type: 'openmodal',
					id: 'modal'
				});
			},

			// 改变成员状态 自定义方法
			async changeStatusFunction(param) {
				let thiz = this;
				let data = param && (param.data || param.data == 0) ? param.data : '';
				let from = param && (param.from || param.from == 0) ? param.from : '';
				let to = param && (param.to || param.to == 0) ? param.to : '';
				const userId = data.user_id;

				this.globalData.changedList[data.user_id] = {
					default: data,
					from: from,
					to: to
				};

				if (data.default == to) {
					delete this.globalData.changedList[data.user_id];

					switch (to) {
						case 'confirm':
						case 'pending':
							this.globalData.list.relation.push(data);
							break;
						case 'unpicked':
							this.globalData.list.request.push(data);
							break;
					}

					return;
				}

				switch (from) {
					case 'confirm':
					case 'pending':
						this.globalData.list.relation = this.globalData.list.relation.filter((v) => {
							return v._id != data._id;
						});
						break;
					case 'unpicked':
						this.globalData.list.request = this.globalData.list.request.filter((v) => {
							return v._id != data._id;
						});
						break;
				}
			},

			// 更新队员 自定义方法
			async updateMemberFunction(param) {
				let thiz = this;
				let option = param && (param.option || param.option == 0) ? param.option : thiz.globalOption || '';
				const data = [];
				for (const user_id in this.globalData.changedList) {
					data.push({
						user_id: user_id,
						from: this.globalData.changedList[user_id].default.default,
						to: this.globalData.changedList[user_id].to
					});
				}

				const res = await uniCloud.importObject('ProjectAction').updateProjectMembers({
					data: data,
					id: option.id,
					user_id: this.$session.getUserValue('user_id')
				});

				this.navigateTo({
					type: 'closemodal',
					id: 'modal1'
				});

				uni.showToast({
					title: res.msg,
					icon: res.status ? 'success' : 'error',
					duration: 2000
				});

				if (res.status) {
					setTimeout(function () {
						uni.redirectTo({
							url: '/pages/in_project/self'
						});
					}, 2000);
				}
			},
			changeCollapse1(evt) {
				let { index } = evt.currentTarget.dataset;
				let collapse1Datas = this.collapse1Datas;

				collapse1Datas[index]['isShow'] = !collapse1Datas[index]['isShow'];
				this.setData({ collapse1Datas });
			},
			stopCloseModal1(e) {
				e.stopPropagation();
			},
			closeModal1() {
				this.modal1 = '';
			},
			stopCloseModal(e) {
				e.stopPropagation();
			},
			closeModal() {
				this.modal = '';
			}
		}
	};
</script>

<style lang="scss" scoped>
	.text7-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		font-size: 30rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.flex-clz {
		flex: 1;
	}
	.text-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		font-size: 28rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text1-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text2-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.button-clz {
		margin-left: 10rpx;
		margin-top: 6rpx;
		margin-bottom: 6rpx;
		margin-right: 10rpx;
	}
	.button-button-clz {
		margin: 6rpx !important;
		padding: 12rpx !important;
	}
	.flex1-clz {
		flex: 1;
	}
	.text4-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		font-size: 28rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text5-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text6-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.button2-clz {
		margin-left: 10rpx;
		margin-top: 6rpx;
		margin-bottom: 6rpx;
		margin-right: 10rpx;
	}
	.button2-button-clz {
		margin: 6rpx !important;
		padding: 12rpx !important;
	}
	.flex19-clz {
		flex: 1;
	}
	.text22-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		font-size: 28rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text23-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.text24-clz {
		margin-left: 10rpx;
		width: calc(100% - 10rpx - 10rpx) !important;
		font-size: 26rpx !important;
		margin-top: 10rpx;
		margin-bottom: 10rpx;
		margin-right: 10rpx;
	}
	.button1-clz {
		margin-left: 10rpx;
		margin-top: 6rpx;
		margin-bottom: 6rpx;
		margin-right: 10rpx;
	}
	.button1-button-clz {
		margin: 6rpx !important;
		padding: 12rpx !important;
	}
	.flex2-clz {
		left: 0rpx;
		bottom: 0rpx;
	}
	.btn-clz {
		background-color: #07c160;
		padding-top: 20rpx;
		color: #fff;
		padding-left: 20rpx;
		padding-bottom: 20rpx;
		text-align: center;
		padding-right: 20rpx;
	}
	.modal1-clz {
		z-index: 1000000;
	}
	.diygw-dialog-modal1 {
	}
	.modal-clz {
		z-index: 1000000;
	}
	.diygw-dialog-modal {
	}
	.ucontent1-clz {
		flex-shrink: 0;
		padding-top: 10rpx;
		padding-left: 10rpx;
		width: 100% !important;
		font-size: 26rpx !important;
		padding-bottom: 10rpx;
		padding-right: 10rpx;
	}
	.container328924 {
	}
</style>
